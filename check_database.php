<?php
// Simple database check script
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Starting database check...\n";

try {
    $pdo = new PDO("mysql:host=localhost;dbname=trust_scholarship_db", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to database successfully!\n";
    
    // Check if file_uploads table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'file_uploads'");
    if ($stmt->rowCount() > 0) {
        echo "✅ file_uploads table exists\n";
        
        // Check current ENUM values
        $stmt = $pdo->query("SHOW COLUMNS FROM file_uploads WHERE Field = 'file_type'");
        $column = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Current file_type ENUM: " . $column['Type'] . "\n";
        
        // Check if we need to update
        $currentEnum = $column['Type'];
        $needsUpdate = false;
        
        $requiredTypes = ['fee_circular', 'aadhaar_card', 'birth_certificate', 'community_certificate', 'course_details', 'attendance_certificate', 'fee_receipts'];
        
        foreach ($requiredTypes as $type) {
            if (strpos($currentEnum, "'$type'") === false) {
                echo "❌ Missing: $type\n";
                $needsUpdate = true;
            } else {
                echo "✅ Found: $type\n";
            }
        }
        
        if ($needsUpdate) {
            echo "\n🔄 Updating file_uploads table...\n";
            
            $sql = "ALTER TABLE file_uploads 
                    MODIFY COLUMN file_type ENUM(
                        'photo',
                        'marksheets',
                        'fee_circular',
                        'signature',
                        'photo_id',
                        'institute_id',
                        'address_proof',
                        'aadhaar_card',
                        'birth_certificate',
                        'community_certificate',
                        'course_details',
                        'income_certificate',
                        'attendance_certificate',
                        'fee_receipts',
                        'disability_certificate',
                        'other'
                    ) NOT NULL";
            
            $pdo->exec($sql);
            echo "✅ file_uploads table updated successfully!\n";
            
            // Verify update
            $stmt = $pdo->query("SHOW COLUMNS FROM file_uploads WHERE Field = 'file_type'");
            $column = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "New file_type ENUM: " . $column['Type'] . "\n";
        } else {
            echo "✅ All file types are already present!\n";
        }
    } else {
        echo "❌ file_uploads table does not exist\n";
    }
    
    // Check applications table
    $stmt = $pdo->query("SHOW TABLES LIKE 'applications'");
    if ($stmt->rowCount() > 0) {
        echo "✅ applications table exists\n";
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'trust_scholarship_db' AND TABLE_NAME = 'applications'");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Applications table has {$result['count']} columns\n";
    } else {
        echo "❌ applications table does not exist\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\nDatabase check complete!\n";
?>
