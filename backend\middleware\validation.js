const { body, param, query, validationResult } = require('express-validator');

// Handle validation errors
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        console.log('❌ Validation failed for request:', req.method, req.url);
        console.log('📋 Request body keys:', Object.keys(req.body));
        console.log('📋 Validation errors:');
        errors.array().forEach(error => {
            console.log(`   - ${error.param}: ${error.msg} (value: ${error.value})`);
        });

        return res.status(400).json({
            success: false,
            error: 'Validation failed',
            details: errors.array()
        });
    }
    console.log('✅ Validation passed for:', req.method, req.url);
    next();
};

// User registration validation
const validateRegistration = [
    body('name')
        .trim()
        .isLength({ min: 2, max: 255 })
        .withMessage('Name must be between 2 and 255 characters'),
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    body('phone')
        .optional()
        .matches(/^[\+]?[1-9][\d]{0,15}$/)
        .withMessage('Please provide a valid phone number'),
    body('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
    handleValidationErrors
];

// User login validation
const validateLogin = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    body('password')
        .notEmpty()
        .withMessage('Password is required'),
    handleValidationErrors
];

// Application validation
const validateApplication = [
    // Personal Details
    body('name')
        .trim()
        .isLength({ min: 2, max: 255 })
        .withMessage('Name must be between 2 and 255 characters'),
    body('gender')
        .isIn(['Male', 'Female', 'Other'])
        .withMessage('Gender must be Male, Female, or Other'),
    body('date_of_birth')
        .isDate()
        .withMessage('Please provide a valid date of birth'),
    body('age')
        .isInt({ min: 1, max: 100 })
        .withMessage('Age must be between 1 and 100'),
    body('place_of_birth')
        .trim()
        .isLength({ min: 2, max: 255 })
        .withMessage('Place of birth is required'),
    body('marital_status')
        .isIn(['Single', 'Married', 'Divorced', 'Widowed'])
        .withMessage('Invalid marital status'),
    body('religion')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Religion is required'),
    body('category')
        .isIn(['BC', 'MBC', 'SC', 'ST', 'General'])
        .withMessage('Invalid category'),
    body('nationality')
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Nationality is required'),
    
    // Address Details
    body('present_address')
        .trim()
        .isLength({ min: 10, max: 500 })
        .withMessage('Present address must be between 10 and 500 characters'),
    body('present_state')
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Present state is required'),
    body('present_country')
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Present country is required'),
    body('permanent_address')
        .trim()
        .isLength({ min: 10, max: 500 })
        .withMessage('Permanent address must be between 10 and 500 characters'),
    body('permanent_state')
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Permanent state is required'),
    body('permanent_country')
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Permanent country is required'),
    
    // Contact Details
    body('phone')
        .isMobilePhone('any')
        .withMessage('Please provide a valid phone number'),
    body('whatsapp')
        .optional()
        .isMobilePhone('any')
        .withMessage('Please provide a valid WhatsApp number'),
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    
    // Family Details
    body('family_total_income')
        .isFloat({ min: 0 })
        .withMessage('Family total income must be a positive number'),
    body('is_disabled')
        .isBoolean()
        .withMessage('Disability status must be true or false'),
    
    // Educational Details
    body('current_course')
        .trim()
        .isLength({ min: 2, max: 255 })
        .withMessage('Current course is required'),
    body('course_duration')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Course duration is required'),
    body('course_year')
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Course year is required'),
    body('roll_number')
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Roll number is required'),
    body('institution_name')
        .trim()
        .isLength({ min: 2, max: 255 })
        .withMessage('Institution name is required'),
    body('institution_type')
        .isIn(['Government', 'Aided', 'Private'])
        .withMessage('Invalid institution type'),
    body('institution_address')
        .trim()
        .isLength({ min: 10, max: 500 })
        .withMessage('Institution address is required'),
    body('institution_phone')
        .isMobilePhone('any')
        .withMessage('Please provide a valid institution phone number'),
    body('institution_email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid institution email'),
    
    // Scholarship Details
    body('scholarship_amount_figures')
        .isFloat({ min: 1 })
        .withMessage('Scholarship amount must be greater than 0'),
    body('scholarship_amount_words')
        .trim()
        .isLength({ min: 5, max: 500 })
        .withMessage('Scholarship amount in words is required'),
    
    // Declaration
    body('declaration_place')
        .trim()
        .isLength({ min: 2, max: 255 })
        .withMessage('Declaration place is required'),
    body('declaration_date')
        .isDate()
        .withMessage('Please provide a valid declaration date'),
    
    handleValidationErrors
];

// File upload validation
const validateFileUpload = [
    body('file_type')
        .isIn(['photo', 'signature', 'photo_id', 'institute_id', 'address_proof',
               'income_certificate', 'disability_certificate', 'marksheets', 'fee_circular',
               'aadhaar_card', 'birth_certificate', 'community_certificate', 'course_details',
               'attendance_certificate', 'fee_receipts', 'other'])
        .withMessage('Invalid file type'),
    handleValidationErrors
];

// Admin application review validation
const validateApplicationReview = [
    body('status')
        .isIn(['approved', 'rejected'])
        .withMessage('Status must be approved or rejected'),
    body('admin_notes')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Admin notes must not exceed 1000 characters'),
    handleValidationErrors
];

// Password reset validation
const validatePasswordReset = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    handleValidationErrors
];

// New password validation
const validateNewPassword = [
    body('token')
        .notEmpty()
        .withMessage('Reset token is required'),
    body('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
    handleValidationErrors
];

// ID parameter validation
const validateId = [
    param('id')
        .isInt({ min: 1 })
        .withMessage('Invalid ID parameter'),
    handleValidationErrors
];

// Pagination validation
const validatePagination = [
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    handleValidationErrors
];

module.exports = {
    validateRegistration,
    validateLogin,
    validateApplication,
    validateFileUpload,
    validateApplicationReview,
    validatePasswordReset,
    validateNewPassword,
    validateId,
    validatePagination,
    handleValidationErrors
};
