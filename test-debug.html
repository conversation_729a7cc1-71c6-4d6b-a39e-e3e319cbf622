<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <h1>Debug Test Page</h1>
    
    <div class="test-section">
        <h2>Test 1: Download Zip Function</h2>
        <p>Current Application ID: <span id="currentAppId">Not Set</span></p>
        <button onclick="setTestAppId()">Set Test Application ID</button>
        <button onclick="testDownloadZip()">Test Download Zip</button>
        <div id="downloadResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Reconsider API</h2>
        <input type="number" id="testAppIdInput" placeholder="Application ID" value="1">
        <button onclick="testReconsiderAPI()">Test Reconsider API</button>
        <div id="reconsiderResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Auth Token</h2>
        <button onclick="checkAuthToken()">Check Auth Token</button>
        <div id="authResult" class="result"></div>
    </div>

    <script>
        let currentApplicationId = null;
        
        function setTestAppId() {
            currentApplicationId = 1; // Set a test ID
            document.getElementById('currentAppId').textContent = currentApplicationId;
            console.log('✅ Set currentApplicationId to:', currentApplicationId);
        }
        
        function testDownloadZip() {
            console.log('🔽 Testing download zip function');
            const result = document.getElementById('downloadResult');
            
            if (!currentApplicationId) {
                result.innerHTML = '❌ No application ID set. Click "Set Test Application ID" first.';
                return;
            }
            
            const authToken = localStorage.getItem('authToken');
            if (!authToken) {
                result.innerHTML = '❌ No auth token found. Please login first.';
                return;
            }
            
            // Test the download URL
            const url = `http://localhost:3000/api/admin/applications/${currentApplicationId}/download-zip?token=${authToken}`;
            result.innerHTML = `✅ Download URL: <a href="${url}" target="_blank">${url}</a>`;
            
            // Try to download
            const link = document.createElement('a');
            link.href = url;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        async function testReconsiderAPI() {
            console.log('🔄 Testing reconsider API');
            const result = document.getElementById('reconsiderResult');
            const appId = document.getElementById('testAppIdInput').value;
            
            if (!appId) {
                result.innerHTML = '❌ Please enter an application ID';
                return;
            }
            
            const authToken = localStorage.getItem('authToken');
            if (!authToken) {
                result.innerHTML = '❌ No auth token found. Please login first.';
                return;
            }
            
            try {
                const response = await fetch(`http://localhost:3000/api/admin/applications/${appId}/reconsider`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        status: 'under_review',
                        admin_comments: 'Test reconsider comment with more than 10 characters'
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    result.innerHTML = `✅ Success: ${JSON.stringify(data)}`;
                } else {
                    const errorText = await response.text();
                    result.innerHTML = `❌ Error ${response.status}: ${errorText}`;
                }
            } catch (error) {
                result.innerHTML = `❌ Network Error: ${error.message}`;
            }
        }
        
        function checkAuthToken() {
            const result = document.getElementById('authResult');
            const authToken = localStorage.getItem('authToken');
            
            if (authToken) {
                result.innerHTML = `✅ Auth token exists (length: ${authToken.length})`;
            } else {
                result.innerHTML = '❌ No auth token found. Please login first.';
            }
        }
    </script>
</body>
</html>
