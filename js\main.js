document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 DOM Content Loaded - Initializing application form...');

    // Check authentication status and update navigation
    updateNavigationBasedOnAuth();

    // Initialize animations
    initializeAnimations();

    // Form navigation
    console.log('🔍 Looking for scholarshipForm element...');
    const form = document.getElementById('scholarshipForm');
    if (!form) {
        console.log('❌ scholarshipForm element not found! Available forms:');
        const allForms = document.querySelectorAll('form');
        allForms.forEach((f, index) => {
            console.log(`   Form ${index + 1}: ID="${f.id}", Class="${f.className}"`);
        });
        return;
    }
    console.log('✅ scholarshipForm element found!');

    // Calculate age from date of birth
    const dobInput = document.getElementById('dob');
    const ageInput = document.getElementById('age');

    if (dobInput && ageInput) {
        dobInput.addEventListener('change', function() {
            const dob = new Date(this.value);
            const today = new Date();
            let age = today.getFullYear() - dob.getFullYear();
            const monthDiff = today.getMonth() - dob.getMonth();

            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
                age--;
            }

            ageInput.value = age;
        });
    }

    // Toggle disability details based on selection
    const isDisabledSelect = document.getElementById('isDisabled');
    const disabilityDetails = document.getElementById('disabilityDetails');

    if (isDisabledSelect && disabilityDetails) {
        isDisabledSelect.addEventListener('change', function() {
            if (this.value === 'true') {
                disabilityDetails.style.display = 'block';
            } else {
                disabilityDetails.style.display = 'none';
            }
        });
    }

    // Handle next button clicks
    const nextButtons = document.querySelectorAll('.btn-next');
    nextButtons.forEach(button => {
        button.addEventListener('click', function() {
            const currentStep = this.closest('.form-step');
            const nextStepNum = this.dataset.next;

            // Validate current step
            if (validateStep(currentStep)) {
                // Save form data to localStorage
                saveFormData();

                // Hide current step
                currentStep.style.display = 'none';

                // Show next step
                const nextStep = document.getElementById(`step${nextStepNum}`);
                if (nextStep) {
                    nextStep.style.display = 'block';

                    // Update progress indicator
                    updateProgress(nextStepNum);

                    // Scroll to top
                    window.scrollTo(0, 0);
                }
            }
        });
    });

    // Handle previous button clicks
    const prevButtons = document.querySelectorAll('.btn-prev');
    prevButtons.forEach(button => {
        button.addEventListener('click', function() {
            const currentStep = this.closest('.form-step');
            const prevStepNum = this.dataset.prev;

            // Hide current step
            currentStep.style.display = 'none';

            // Show previous step
            const prevStep = document.getElementById(`step${prevStepNum}`);
            if (prevStep) {
                prevStep.style.display = 'block';

                // Update progress indicator
                updateProgress(prevStepNum);

                // Scroll to top
                window.scrollTo(0, 0);
            }
        });
    });

    // Photo upload preview
    const photoInput = document.getElementById('photo');
    const photoPlaceholder = document.querySelector('.photo-placeholder');

    if (photoInput && photoPlaceholder) {
        photoInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Remove placeholder text
                    while (photoPlaceholder.firstChild) {
                        photoPlaceholder.removeChild(photoPlaceholder.firstChild);
                    }

                    // Create image element
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.style.maxWidth = '100%';
                    img.style.maxHeight = '100%';
                    photoPlaceholder.appendChild(img);

                    // Add change photo button
                    const label = document.createElement('label');
                    label.setAttribute('for', 'photo');
                    label.textContent = 'Change Photo';
                    label.style.marginTop = '10px';
                    photoPlaceholder.appendChild(label);
                };
                reader.readAsDataURL(file);
            }
        });
    }

    // Handle file input labels
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        if (input.id !== 'photo') { // Skip photo input as it's handled separately
            input.addEventListener('change', function() {
                const label = this.previousElementSibling;
                if (this.files.length > 0) {
                    if (this.files.length === 1) {
                        label.textContent = `Selected: ${this.files[0].name}`;
                    } else {
                        label.textContent = `Selected: ${this.files.length} files`;
                    }
                } else {
                    label.textContent = label.getAttribute('data-default-text') || 'Choose file';
                }
            });

            // Store original label text
            const label = input.previousElementSibling;
            if (label) {
                label.setAttribute('data-default-text', label.textContent);
            }
        }
    });

    // Form submission
    if (form) {
        console.log('📋 Form element found, adding submit listener');
        console.log('📋 Form ID:', form.id);
        console.log('📋 Form action:', form.action);
        console.log('📋 Form method:', form.method);

        form.addEventListener('submit', function(e) {
            console.log('🚀 Form submit event triggered!');
            e.preventDefault();

            // Comprehensive field validation
            console.log('🔍 Starting comprehensive field validation...');
            const validationResult = validateAllFields();

            if (!validationResult.isValid) {
                console.log('❌ Validation failed:', validationResult.errors);
                displayValidationErrors(validationResult.errors);

                // Navigate to the first error field
                if (validationResult.firstErrorField) {
                    navigateToFieldStep(validationResult.firstErrorField);
                    validationResult.firstErrorField.focus();
                }
                return;
            }

            console.log('✅ All fields validated successfully');
            console.log('📤 Starting form submission process...');
                // Show loading message
                showMessage('Submitting your application...', 'info');

                // Collect form data
                console.log('📊 Collecting form data...');
                const formData = new FormData(form);

                // Convert FormData to JSON object (exclude file uploads for now)
                const applicationData = {};
                let fieldCount = 0;
                for (let [key, value] of formData.entries()) {
                    // Skip file inputs and empty values
                    if (key !== 'photo' && key !== 'signature' &&
                        !key.includes('ID') && !key.includes('Proof') &&
                        !key.includes('Certificate') && !key.includes('File') &&
                        !key.includes('marksheets') && !key.includes('feeCircular') &&
                        value && value.trim && value.trim() !== '') {
                        applicationData[key] = value;
                        fieldCount++;
                    }
                }
                console.log(`📋 Collected ${fieldCount} non-file form fields`);
                console.log('📋 Form data keys:', Object.keys(applicationData));

                // Log some sample values for debugging
                console.log('📋 Sample values:', {
                    name: applicationData.name,
                    email: applicationData.email,
                    phone: applicationData.phone,
                    currentCourse: applicationData.currentCourse
                });

                // Map form field names to database field names
                console.log('🗂️ Mapping form data to database fields...');

                // Get user info from localStorage
                const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
                console.log('👤 Current user info:', currentUser);

                const mappedData = {
                    // Add user_id if available
                    user_id: currentUser.id || null,
                    // Add common required fields
                    status: 'draft',
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString(),
                    // Personal Information
                    name: applicationData.name || '',
                    gender: applicationData.gender || '',
                    date_of_birth: applicationData.dob || '',
                    age: applicationData.age ? parseInt(applicationData.age) : 0,
                    place_of_birth: applicationData.birthplace || '',
                    marital_status: applicationData.maritalStatus || '',
                    religion: applicationData.religion || '',
                    category: applicationData.category || '',
                    nationality: applicationData.nationality || '',

                    // Address Information
                    present_address: applicationData.presentAddress || '',
                    present_state: applicationData.presentState || '',
                    present_country: applicationData.presentCountry || '',
                    permanent_address: applicationData.permanentAddress || '',
                    permanent_state: applicationData.permanentState || '',
                    permanent_country: applicationData.permanentCountry || '',

                    // Contact Information
                    phone: applicationData.phone || '',
                    whatsapp: applicationData.whatsapp || applicationData.phone || '',
                    email: applicationData.email || '',

                    // Family Information
                    family_total_income: applicationData.familyIncome ? parseFloat(applicationData.familyIncome) : 0,
                    father_name: applicationData.father_name || '',
                    father_age: applicationData.father_age || '',
                    father_occupation: applicationData.father_occupation || '',
                    father_income: applicationData.father_income ? parseFloat(applicationData.father_income) : 0,
                    father_employment: applicationData.father_employment || '',
                    mother_name: applicationData.mother_name || '',
                    mother_age: applicationData.mother_age || '',
                    mother_occupation: applicationData.mother_occupation || '',
                    mother_income: applicationData.mother_income ? parseFloat(applicationData.mother_income) : 0,
                    mother_employment: applicationData.mother_employment || '',
                    spouse_name: applicationData.spouse_name || '',
                    spouse_age: applicationData.spouse_age || '',
                    spouse_occupation: applicationData.spouse_occupation || '',
                    spouse_income: applicationData.spouse_income ? parseFloat(applicationData.spouse_income) : 0,
                    spouse_employment: applicationData.spouse_employment || '',
                    sibling1_name: applicationData.sibling1_name || '',
                    sibling1_age: applicationData.sibling1_age || '',
                    sibling1_occupation: applicationData.sibling1_occupation || '',
                    sibling1_income: applicationData.sibling1_income ? parseFloat(applicationData.sibling1_income) : 0,
                    sibling1_employment: applicationData.sibling1_employment || '',
                    sibling2_name: applicationData.sibling2_name || '',
                    sibling2_age: applicationData.sibling2_age || '',
                    sibling2_occupation: applicationData.sibling2_occupation || '',
                    sibling2_income: applicationData.sibling2_income ? parseFloat(applicationData.sibling2_income) : 0,
                    sibling2_employment: applicationData.sibling2_employment || '',
                    sibling3_name: applicationData.sibling3_name || '',
                    sibling3_age: applicationData.sibling3_age || '',
                    sibling3_occupation: applicationData.sibling3_occupation || '',
                    sibling3_income: applicationData.sibling3_income ? parseFloat(applicationData.sibling3_income) : 0,
                    sibling3_employment: applicationData.sibling3_employment || '',
                    sibling4_name: applicationData.sibling4_name || '',
                    sibling4_age: applicationData.sibling4_age || '',
                    sibling4_occupation: applicationData.sibling4_occupation || '',
                    sibling4_income: applicationData.sibling4_income ? parseFloat(applicationData.sibling4_income) : 0,
                    sibling4_employment: applicationData.sibling4_employment || '',

                    // Disability Information
                    is_disabled: applicationData.isDisabled === 'true' ? true : false,
                    disability_type: applicationData.disability_type || '',
                    disability_percentage: applicationData.disability_percentage ? parseInt(applicationData.disability_percentage) : null,
                    disability_description: applicationData.disability_description || '',
                    issuing_authority: applicationData.issuingAuthority || '',
                    certificate_number: applicationData.certificateNumber || '',
                    issue_date: applicationData.issueDate || '',

                    // Educational Information - SSLC
                    sslc_institution: applicationData.sslc_institution || '',
                    sslc_type: applicationData.sslc_type || '',
                    sslc_board: applicationData.sslc_board || '',
                    sslc_marks: applicationData.sslc_marks || '',
                    sslc_year: applicationData.sslc_year ? parseInt(applicationData.sslc_year) : null,

                    // Educational Information - HSC
                    hsc_institution: applicationData.hsc_institution || '',
                    hsc_type: applicationData.hsc_type || '',
                    hsc_board: applicationData.hsc_board || '',
                    hsc_marks: applicationData.hsc_marks || '',
                    hsc_year: applicationData.hsc_year ? parseInt(applicationData.hsc_year) : null,

                    // Educational Information - UG
                    ug_institution: applicationData.ug_institution || '',
                    ug_type: applicationData.ug_type || '',
                    ug_board: applicationData.ug_board || '',
                    ug_marks: applicationData.ug_marks || '',
                    ug_year: applicationData.ug_year ? parseInt(applicationData.ug_year) : null,

                    // Educational Information - Diploma
                    diploma_institution: applicationData.diploma_institution || '',
                    diploma_type: applicationData.diploma_type || '',
                    diploma_board: applicationData.diploma_board || '',
                    diploma_marks: applicationData.diploma_marks || '',
                    diploma_year: applicationData.diploma_year ? parseInt(applicationData.diploma_year) : null,

                    // Educational Information - Vocational
                    vocational_institution: applicationData.vocational_institution || '',
                    vocational_type: applicationData.vocational_type || '',
                    vocational_board: applicationData.vocational_board || '',
                    vocational_marks: applicationData.vocational_marks || '',
                    vocational_year: applicationData.vocational_year ? parseInt(applicationData.vocational_year) : null,

                    // Educational Information - Others
                    others_institution: applicationData.others_institution || '',
                    others_type: applicationData.others_type || '',
                    others_board: applicationData.others_board || '',
                    others_marks: applicationData.others_marks || '',
                    others_year: applicationData.others_year ? parseInt(applicationData.others_year) : null,

                    // Current Studies
                    current_course: applicationData.currentCourse || '',
                    course_duration: applicationData.courseDuration || '',
                    course_year: applicationData.courseYear || '',
                    roll_number: applicationData.rollNo || '',
                    institution_name: applicationData.institutionName || '',
                    institution_type: applicationData.institutionType || '',
                    institution_address: applicationData.institutionAddress || '',
                    institution_phone: applicationData.institutionPhone || '',
                    institution_email: applicationData.institutionEmail || '',
                    institution_website: applicationData.institutionWebsite || '',
                    term_fees: applicationData.termFees || '',
                    tuition_fees: applicationData.tuitionFees ? parseFloat(applicationData.tuitionFees) : 0,
                    other_fees: applicationData.otherFees ? parseFloat(applicationData.otherFees) : 0,

                    // Scholarship Information
                    scholarship_amount_figures: applicationData.amountFigures ? parseFloat(applicationData.amountFigures) : 0,
                    scholarship_amount_words: applicationData.amountWords || '',
                    previous_awt_scholarship: applicationData.previousAWTScholarship || '',
                    other_scholarships: applicationData.otherScholarships || '',
                    applied_scholarships: applicationData.appliedScholarships || '',

                    // References
                    reference1_name: applicationData.reference1_name || '',
                    reference1_phone: applicationData.reference1_phone || '',
                    reference1_email: applicationData.reference1_email || '',
                    reference1_position: applicationData.reference1_position || '',
                    reference2_name: applicationData.reference2_name || '',
                    reference2_phone: applicationData.reference2_phone || '',
                    reference2_email: applicationData.reference2_email || '',
                    reference2_position: applicationData.reference2_position || '',

                    // Additional Information
                    extracurricular: applicationData.extracurricular || '',
                    goals: applicationData.goals || '',
                    other_info: applicationData.otherInfo || '',

                    // Declaration
                    declaration_place: applicationData.place || '',
                    declaration_date: applicationData.date || ''
                };

                // Handle permanent address copying if needed
                const sameAsPresentCheckbox = form.querySelector('#sameAsPresent');
                const isPermanentSameAsPresent = sameAsPresentCheckbox && sameAsPresentCheckbox.checked;

                if (isPermanentSameAsPresent) {
                    console.log('🏠 Copying present address to permanent address');
                    // Copy present address to permanent address in mapped data
                    mappedData.permanent_address = mappedData.present_address;
                    mappedData.permanent_state = mappedData.present_state;
                    mappedData.permanent_country = mappedData.present_country;
                }

                console.log('📋 Mapped data preview:', {
                    name: mappedData.name,
                    email: mappedData.email,
                    phone: mappedData.phone,
                    course: mappedData.current_course,
                    institution: mappedData.institution_name
                });

                // Submit to backend API
                console.log('🚀 Calling submitApplication function...');
                submitApplication(mappedData);
        });
    } else {
        console.log('❌ Form element not found!');
    }

    // Add click listener to submit button for debugging
    const submitButton = document.querySelector('.btn-submit');
    if (submitButton) {
        console.log('🔘 Submit button found, adding click listener');
        console.log('🔘 Submit button type:', submitButton.type);
        console.log('🔘 Submit button form:', submitButton.form);
        console.log('🔘 Submit button parent:', submitButton.parentElement);

        submitButton.addEventListener('click', function(e) {
            console.log('🖱️ Submit button clicked!');
            // Let the browser handle the form submission naturally
        });
    } else {
        console.log('❌ Submit button not found!');
        console.log('❌ Available buttons:', document.querySelectorAll('button'));
    }

    // Helper function to get user-friendly field labels
    function getFieldLabel(field) {
        // Try to get label from associated label element
        const fieldId = field.id || field.name;
        if (fieldId) {
            const label = document.querySelector(`label[for="${fieldId}"]`);
            if (label) {
                return label.textContent.trim();
            }
        }

        // Try to get label from parent label element
        const parentLabel = field.closest('label');
        if (parentLabel) {
            return parentLabel.textContent.trim();
        }

        // Try to get from previous sibling label
        let prevElement = field.previousElementSibling;
        while (prevElement) {
            if (prevElement.tagName === 'LABEL') {
                return prevElement.textContent.trim();
            }
            prevElement = prevElement.previousElementSibling;
        }

        // Fallback to placeholder or field name
        return field.placeholder || field.name || field.id || 'Unknown field';
    }

    // Submit application to backend API
    async function submitApplication(applicationData) {
        try {
            console.log('🔄 Starting application submission...');
            console.log('📋 Application data:', applicationData);

            const authToken = localStorage.getItem('authToken');
            console.log('🔐 Auth token found:', authToken ? 'Yes' : 'No');

            if (!authToken) {
                console.log('❌ No auth token found, redirecting to login');
                showMessage('Please login to submit your application.', 'error');
                window.location.href = 'login-signup.html';
                return;
            }

            // Step 1: Submit application data first
            console.log('📡 Step 1: Submitting application data...');
            console.log('📡 API endpoint:', '/api/applications');
            console.log('📡 Auth token (first 20 chars):', authToken ? authToken.substring(0, 20) + '...' : 'None');
            console.log('📡 Data being sent:', JSON.stringify(applicationData, null, 2));
            showMessage('Submitting application data...', 'info');

            const response = await apiCall('/api/applications', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify(applicationData)
            });

            console.log('📡 Response status:', response.status);
            console.log('📡 Response headers:', response.headers);

            if (!response.ok) {
                console.log('⚠️ Response not OK, status:', response.status);
                const errorText = await response.text();
                console.log('❌ Error response:', errorText);
                console.log('❌ Request URL:', response.url);
                console.log('❌ Request headers sent:', {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken ? authToken.substring(0, 20) + '...' : 'None'}`
                });
                console.log('❌ Data sent to server:', JSON.stringify(applicationData, null, 2));

                try {
                    const errorData = JSON.parse(errorText);
                    console.log('❌ Parsed error data:', errorData);
                    showMessage(errorData.error || `Server error: ${response.status}`, 'error');
                } catch (parseError) {
                    console.log('❌ Could not parse error response as JSON');
                    showMessage(`Server error: ${response.status} - ${errorText}`, 'error');
                }
                return;
            }

            const result = await response.json();
            console.log('✅ Application submission success:', result);

            if (result.success) {
                const applicationId = result.data.id;
                console.log('🎉 Application created successfully! ID:', applicationId);

                // Step 2: Submit the application (change status from draft to submitted)
                console.log('📡 Step 2: Submitting application for review...');
                showMessage('Submitting application for review...', 'info');

                const submitResponse = await apiCall(`/api/applications/${applicationId}/submit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (!submitResponse.ok) {
                    console.log('⚠️ Submit response not OK, status:', submitResponse.status);
                    const errorText = await submitResponse.text();
                    console.log('❌ Submit error response:', errorText);
                    showMessage('Application created but failed to submit. Please try again.', 'error');
                    return;
                }

                const submitResult = await submitResponse.json();
                console.log('✅ Application submitted for review:', submitResult);

                // Step 3: Upload files if any
                const fileInputs = form.querySelectorAll('input[type="file"]');
                const filesToUpload = [];

                fileInputs.forEach(input => {
                    if (input.files && input.files.length > 0) {
                        for (let file of input.files) {
                            filesToUpload.push({
                                file: file,
                                type: getFileType(input.name),
                                inputName: input.name
                            });
                        }
                    }
                });

                if (filesToUpload.length > 0) {
                    console.log(`📎 Found ${filesToUpload.length} files to upload`);
                    showMessage(`Uploading ${filesToUpload.length} files...`, 'info');

                    await uploadFiles(filesToUpload, applicationId, authToken);
                }

                showMessage('Application and files submitted successfully!', 'success');

                // Clear saved form data
                localStorage.removeItem('scholarshipFormData');
                console.log('🧹 Cleared saved form data');

                // Redirect to confirmation page after 3 seconds
                setTimeout(() => {
                    console.log('🏠 Redirecting to dashboard...');
                    window.location.href = 'candidate-dashboard.html';
                }, 3000);
            } else {
                console.log('❌ Application submission failed:', result.error);
                showMessage(result.error || 'Failed to submit application. Please try again.', 'error');
            }
        } catch (error) {
            console.error('❌ Application submission error:', error);
            console.error('Error details:', error.message);
            console.error('Error stack:', error.stack);
            showMessage(`Network error: ${error.message}. Please check your connection and try again.`, 'error');
        }
    }

    // File upload functions
    function getFileType(inputName) {
        const fileTypeMap = {
            'photo': 'photo',
            'signature': 'signature',
            'photoID': 'photo_id',
            'instituteID': 'institute_id',
            'addressProof': 'address_proof',
            'aadhaarCard': 'aadhaar_card',
            'birthCertificate': 'birth_certificate',
            'communityCertificate': 'community_certificate',
            'courseDetails': 'course_details',
            'incomeCertificate': 'income_certificate',
            'attendanceCertificate': 'attendance_certificate',
            'feeReceipts': 'fee_receipts',
            'marksheets': 'marksheets',
            'feeCircular': 'fee_circular',
            'disabilityCertificateFile': 'disability_certificate'
        };
        return fileTypeMap[inputName] || 'other';
    }

    async function uploadFiles(filesToUpload, applicationId, authToken) {
        let uploadedCount = 0;
        let failedCount = 0;

        for (let fileData of filesToUpload) {
            try {
                console.log(`📤 Uploading file: ${fileData.file.name} (${fileData.type})`);

                const formData = new FormData();
                formData.append('file', fileData.file);
                formData.append('file_type', fileData.type);
                formData.append('application_id', applicationId);

                const uploadResponse = await apiCall('/api/files/upload', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: formData
                });

                if (uploadResponse.ok) {
                    uploadedCount++;
                    console.log(`✅ File uploaded successfully: ${fileData.file.name}`);
                } else {
                    failedCount++;
                    console.log(`❌ File upload failed: ${fileData.file.name}`);
                }
            } catch (error) {
                failedCount++;
                console.error(`❌ File upload error for ${fileData.file.name}:`, error);
            }
        }

        console.log(`📊 File upload summary: ${uploadedCount} successful, ${failedCount} failed`);

        if (failedCount > 0) {
            showMessage(`Application submitted! ${uploadedCount} files uploaded, ${failedCount} files failed. You can upload missing files later.`, 'info');
        }
    }

    // Show message function
    function showMessage(message, type) {
        // Remove existing message
        const existingMessage = document.querySelector('.form-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // Create message element
        const messageDiv = document.createElement('div');
        messageDiv.className = `form-message ${type}`;

        // Handle multi-line messages
        if (message.includes('\n')) {
            // Convert line breaks to HTML
            const htmlMessage = message.split('\n').map(line => {
                if (line.trim() === '') return '<br>';
                return `<div>${line}</div>`;
            }).join('');
            messageDiv.innerHTML = htmlMessage;
        } else {
            messageDiv.textContent = message;
        }

        let styles = '';
        switch(type) {
            case 'success':
                styles = 'background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb;';
                break;
            case 'error':
                styles = 'background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;';
                break;
            case 'info':
                styles = 'background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb;';
                break;
            default:
                styles = 'background-color: #f8f9fa; color: #495057; border: 1px solid #dee2e6;';
        }

        messageDiv.style.cssText = `
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 4px;
            text-align: center;
            font-weight: 500;
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            min-width: 300px;
            ${styles}
        `;

        // Insert message at the top of the page
        document.body.insertBefore(messageDiv, document.body.firstChild);

        // Auto-remove message after 5 seconds (except for info messages which auto-remove after 3 seconds)
        const timeout = type === 'info' ? 3000 : 5000;
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.remove();
            }
        }, timeout);
    }

    // Initialize file upload functionality
    initializeFileUploads();

    // Load saved form data if available
    loadFormData();

    // Comprehensive field validation function
    function validateAllFields() {
        const errors = [];
        let firstErrorField = null;

        // Get form element
        const form = document.getElementById('scholarshipForm');
        if (!form) {
            return { isValid: false, errors: ['Form not found'], firstErrorField: null };
        }

        // Define validation rules based on database schema
        const validationRules = {
            // Step 1: Personal Details
            name: { required: true, minLength: 2, maxLength: 255, pattern: /^[a-zA-Z\s.'-]+$/, message: 'Name must contain only letters, spaces, dots, apostrophes, and hyphens' },
            gender: { required: true, options: ['Male', 'Female', 'Other'] },
            dob: { required: true, type: 'date', maxDate: new Date() },
            age: { required: true, type: 'number', min: 1, max: 100 },
            birthplace: { required: true, minLength: 2, maxLength: 255 },
            maritalStatus: { required: true, options: ['Single', 'Married', 'Divorced', 'Widowed'] },
            religion: { required: true, minLength: 2, maxLength: 255 },
            category: { required: true, options: ['General', 'OBC', 'SC', 'ST', 'Other'] },
            nationality: { required: true, minLength: 2, maxLength: 255 },

            // Address fields
            presentAddress: { required: true, minLength: 10, maxLength: 500 },
            presentState: { required: true, minLength: 2, maxLength: 255 },
            presentCountry: { required: true, minLength: 2, maxLength: 255 },
            permanentAddress: { required: true, minLength: 10, maxLength: 500 },
            permanentState: { required: true, minLength: 2, maxLength: 255 },
            permanentCountry: { required: true, minLength: 2, maxLength: 255 },

            // Contact fields
            phone: { required: true, pattern: /^[0-9]{10}$/, message: 'Phone number must be exactly 10 digits' },
            email: { required: true, type: 'email', maxLength: 255 },

            // Family details
            familyIncome: { required: true, type: 'number', min: 0, max: 99999999.99 },
            isDisabled: { required: true, options: ['true', 'false'] },

            // Step 3: Educational Details
            currentCourse: { required: true, minLength: 2, maxLength: 255 },
            courseDuration: { required: true, minLength: 1, maxLength: 255 },
            courseYear: { required: true, minLength: 1, maxLength: 255 },
            rollNo: { required: true, minLength: 1, maxLength: 255 },
            institutionName: { required: true, minLength: 2, maxLength: 255 },
            institutionType: { required: true, options: ['Government', 'Aided', 'Private'] },
            institutionAddress: { required: true, minLength: 10, maxLength: 500 },
            institutionPhone: { required: true, pattern: /^[0-9]{10}$/, message: 'Institution phone must be exactly 10 digits' },
            institutionEmail: { required: true, type: 'email', maxLength: 255 },
            termFees: { required: true, minLength: 1, maxLength: 255 },
            tuitionFees: { required: true, type: 'number', min: 0, max: 9999999.99 },
            otherFees: { required: true, type: 'number', min: 0, max: 9999999.99 },

            // Step 4: Scholarship Details
            amountFigures: { required: true, type: 'number', min: 1, max: 9999999.99 },
            amountWords: { required: true, minLength: 2, maxLength: 255 },

            // Step 5: References
            reference1_name: { required: true, minLength: 2, maxLength: 255, pattern: /^[a-zA-Z\s.'-]+$/, message: 'Reference 1 name must contain only letters, spaces, dots, apostrophes, and hyphens' },
            reference1_phone: { required: true, pattern: /^[0-9]{10}$/, message: 'Reference 1 phone must be exactly 10 digits' },
            reference1_email: { required: true, type: 'email', maxLength: 255 },
            reference1_position: { required: true, options: ['Teacher', 'Principal', 'HOD', 'Professor', 'Employer', 'Other'] },
            reference2_name: { required: true, minLength: 2, maxLength: 255, pattern: /^[a-zA-Z\s.'-]+$/, message: 'Reference 2 name must contain only letters, spaces, dots, apostrophes, and hyphens' },
            reference2_phone: { required: true, pattern: /^[0-9]{10}$/, message: 'Reference 2 phone must be exactly 10 digits' },
            reference2_email: { required: true, type: 'email', maxLength: 255 },
            reference2_position: { required: true, options: ['Teacher', 'Principal', 'HOD', 'Professor', 'Employer', 'Other'] },

            // Goals and Declaration
            goals: { required: true, minLength: 10, maxLength: 2000 },
            place: { required: true, minLength: 2, maxLength: 255 },
            date: { required: true, type: 'date', maxDate: new Date() },

            // Terms agreement
            termsAgreed: { required: true, type: 'checkbox' }
        };

        // Validate each field
        Object.keys(validationRules).forEach(fieldName => {
            const field = form.querySelector(`[name="${fieldName}"]`);
            const rules = validationRules[fieldName];

            if (!field) return;

            const value = field.value ? field.value.trim() : '';
            const fieldLabel = getFieldLabel(field);

            // Required field validation
            if (rules.required && !value) {
                errors.push(`${fieldLabel} is required`);
                if (!firstErrorField) firstErrorField = field;
                field.classList.add('error');
                return;
            }

            // Skip other validations if field is empty and not required
            if (!value && !rules.required) return;

            // Length validations
            if (rules.minLength && value.length < rules.minLength) {
                errors.push(`${fieldLabel} must be at least ${rules.minLength} characters long`);
                if (!firstErrorField) firstErrorField = field;
                field.classList.add('error');
            }

            if (rules.maxLength && value.length > rules.maxLength) {
                errors.push(`${fieldLabel} must not exceed ${rules.maxLength} characters`);
                if (!firstErrorField) firstErrorField = field;
                field.classList.add('error');
            }

            // Pattern validation
            if (rules.pattern && !rules.pattern.test(value)) {
                errors.push(rules.message || `${fieldLabel} format is invalid`);
                if (!firstErrorField) firstErrorField = field;
                field.classList.add('error');
            }

            // Options validation (for select fields)
            if (rules.options && !rules.options.includes(value)) {
                errors.push(`Please select a valid option for ${fieldLabel}`);
                if (!firstErrorField) firstErrorField = field;
                field.classList.add('error');
            }

            // Number validations
            if (rules.type === 'number') {
                const numValue = parseFloat(value);
                if (isNaN(numValue)) {
                    errors.push(`${fieldLabel} must be a valid number`);
                    if (!firstErrorField) firstErrorField = field;
                    field.classList.add('error');
                } else {
                    if (rules.min !== undefined && numValue < rules.min) {
                        errors.push(`${fieldLabel} must be at least ${rules.min}`);
                        if (!firstErrorField) firstErrorField = field;
                        field.classList.add('error');
                    }
                    if (rules.max !== undefined && numValue > rules.max) {
                        errors.push(`${fieldLabel} must not exceed ${rules.max}`);
                        if (!firstErrorField) firstErrorField = field;
                        field.classList.add('error');
                    }
                }
            }

            // Date validation
            if (rules.type === 'date') {
                const dateValue = new Date(value);
                if (isNaN(dateValue.getTime())) {
                    errors.push(`${fieldLabel} must be a valid date`);
                    if (!firstErrorField) firstErrorField = field;
                    field.classList.add('error');
                } else {
                    if (rules.maxDate && dateValue > rules.maxDate) {
                        errors.push(`${fieldLabel} cannot be in the future`);
                        if (!firstErrorField) firstErrorField = field;
                        field.classList.add('error');
                    }
                }
            }

            // Email validation
            if (rules.type === 'email') {
                const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailPattern.test(value)) {
                    errors.push(`${fieldLabel} must be a valid email address`);
                    if (!firstErrorField) firstErrorField = field;
                    field.classList.add('error');
                }
            }

            // Checkbox validation
            if (rules.type === 'checkbox') {
                if (!field.checked) {
                    errors.push(`You must agree to the terms and conditions`);
                    if (!firstErrorField) firstErrorField = field;
                    field.classList.add('error');
                }
            }

            // Remove error class if field is valid
            if (errors.length === 0 || !errors.some(error => error.includes(fieldLabel))) {
                field.classList.remove('error');
            }
        });

        // Validate required file uploads
        const requiredFiles = [
            { name: 'signature', label: 'Signature' },
            { name: 'feeCircular', label: 'Fee Circular' },
            { name: 'photoID', label: 'Photo ID' },
            { name: 'instituteID', label: 'Institute ID' },
            { name: 'addressProof', label: 'Address Proof' },
            { name: 'aadhaarCard', label: 'Aadhaar Card' },
            { name: 'birthCertificate', label: 'Birth Certificate' },
            { name: 'communityCertificate', label: 'Community Certificate' },
            { name: 'courseDetails', label: 'Course Fee Details' },
            { name: 'incomeCertificate', label: 'Income Certificate' }
        ];

        requiredFiles.forEach(fileInfo => {
            const fileInput = form.querySelector(`[name="${fileInfo.name}"]`);
            if (fileInput && (!fileInput.files || fileInput.files.length === 0)) {
                errors.push(`${fileInfo.label} is required`);
                if (!firstErrorField) firstErrorField = fileInput;
                fileInput.classList.add('error');
            } else if (fileInput && fileInput.files && fileInput.files.length > 0) {
                // Validate file type and size
                const file = fileInput.files[0];
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
                const maxSize = 10 * 1024 * 1024; // 10MB

                if (!allowedTypes.includes(file.type)) {
                    errors.push(`${fileInfo.label} must be a JPG, PNG, or PDF file`);
                    if (!firstErrorField) firstErrorField = fileInput;
                    fileInput.classList.add('error');
                }

                if (file.size > maxSize) {
                    errors.push(`${fileInfo.label} must be smaller than 10MB`);
                    if (!firstErrorField) firstErrorField = fileInput;
                    fileInput.classList.add('error');
                }

                if (file.size === 0) {
                    errors.push(`${fileInfo.label} appears to be empty`);
                    if (!firstErrorField) firstErrorField = fileInput;
                    fileInput.classList.add('error');
                }
            }
        });

        return {
            isValid: errors.length === 0,
            errors: errors,
            firstErrorField: firstErrorField
        };
    }

    // Display validation errors function
    function displayValidationErrors(errors) {
        // Create error message
        let errorMessage = `❌ Please fix the following errors:\n\n`;
        errors.forEach((error, index) => {
            errorMessage += `${index + 1}. ${error}\n`;
        });

        // Show error message
        showMessage(errorMessage, 'error');

        // Also log to console for debugging
        console.log('🔍 Validation Errors:', errors);
    }

    // Navigate to field step function
    function navigateToFieldStep(field) {
        if (!field) return;

        // Find which step the field belongs to
        const fieldStep = field.closest('.form-step');
        if (fieldStep) {
            const stepNumber = fieldStep.id.replace('step', '');
            console.log(`🔄 Navigating to step ${stepNumber} for field validation error`);
            showStep(parseInt(stepNumber));

            // Scroll to field after a short delay
            setTimeout(() => {
                field.scrollIntoView({ behavior: 'smooth', block: 'center' });

                // Add visual highlight
                field.style.border = '2px solid #dc3545';
                field.style.boxShadow = '0 0 5px rgba(220, 53, 69, 0.5)';

                // Remove highlight after 3 seconds
                setTimeout(() => {
                    field.style.border = '';
                    field.style.boxShadow = '';
                }, 3000);
            }, 300);
        }
    }

    // Enhanced field label function
    function getFieldLabel(field) {
        // Try to find associated label
        const fieldId = field.id || field.name;
        if (fieldId) {
            const label = document.querySelector(`label[for="${fieldId}"]`);
            if (label) {
                // Clean up label text (remove numbers and extra characters)
                let labelText = label.textContent.trim();
                labelText = labelText.replace(/^\d+\.\s*/, ''); // Remove leading numbers
                labelText = labelText.replace(/\s*\([^)]*\)$/, ''); // Remove trailing parentheses
                return labelText;
            }
        }

        // Try to find parent label
        const parentLabel = field.closest('label');
        if (parentLabel) {
            let labelText = parentLabel.textContent.trim();
            labelText = labelText.replace(/^\d+\.\s*/, '');
            labelText = labelText.replace(/\s*\([^)]*\)$/, '');
            return labelText;
        }

        // Try to find previous sibling label
        let prevElement = field.previousElementSibling;
        while (prevElement) {
            if (prevElement.tagName === 'LABEL') {
                let labelText = prevElement.textContent.trim();
                labelText = labelText.replace(/^\d+\.\s*/, '');
                labelText = labelText.replace(/\s*\([^)]*\)$/, '');
                return labelText;
            }
            prevElement = prevElement.previousElementSibling;
        }

        // Fallback to field name with proper formatting
        const fieldName = field.name || field.id || 'Unknown field';
        return fieldName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    }

    // Simple test function for debugging
    window.testFormSubmission = function() {
        console.log('🧪 Testing form submission...');
        const form = document.getElementById('scholarshipForm');
        if (form) {
            console.log('✅ Form found');
            const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
            form.dispatchEvent(submitEvent);
        } else {
            console.log('❌ Form not found');
        }
    };

    // Test backend connection
    window.testBackendConnection = async function() {
        console.log('🧪 Testing backend connection...');
        const authToken = localStorage.getItem('authToken');
        console.log('🔐 Auth token found:', authToken ? 'Yes' : 'No');

        if (!authToken) {
            console.log('❌ No auth token - please login first');
            return;
        }

        try {
            const response = await fetch('http://localhost:3000/api/applications', {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('📡 Response status:', response.status);
            const responseText = await response.text();
            console.log('📡 Response:', responseText);

            if (response.ok) {
                console.log('✅ Backend connection successful');
            } else {
                console.log('❌ Backend connection failed');
            }
        } catch (error) {
            console.log('❌ Network error:', error.message);
        }
    };

    // Test with minimal application data
    window.testMinimalApplication = async function() {
        console.log('🧪 Testing minimal application creation...');
        const authToken = localStorage.getItem('authToken');

        if (!authToken) {
            console.log('❌ No auth token - please login first');
            return;
        }

        const minimalData = {
            name: 'Test User',
            email: '<EMAIL>',
            phone: '1234567890',
            current_course: 'Test Course',
            institution_name: 'Test Institution'
        };

        console.log('📡 Sending minimal data:', minimalData);

        try {
            const response = await fetch('http://localhost:3000/api/applications', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(minimalData)
            });

            console.log('📡 Response status:', response.status);
            const responseText = await response.text();
            console.log('📡 Response:', responseText);

            if (response.ok) {
                console.log('✅ Minimal application creation successful');
            } else {
                console.log('❌ Minimal application creation failed');
            }
        } catch (error) {
            console.log('❌ Network error:', error.message);
        }
    };

    // Add test data function
    window.fillTestData = function() {
        console.log('🧪 Filling test data...');
        const testData = {
            name: 'Test User',
            gender: 'Male',
            dob: '1995-01-01',
            age: '29',
            birthplace: 'Test City',
            maritalStatus: 'Single',
            religion: 'Test Religion',
            category: 'General',
            nationality: 'Indian',
            presentAddress: 'Test Present Address, House No 123, Street Name, Test City, Test State, PIN 123456',
            presentState: 'Test State',
            presentCountry: 'India',
            permanentAddress: 'Test Permanent Address, House No 456, Street Name, Test City, Test State, PIN 123456',
            permanentState: 'Test State',
            permanentCountry: 'India',
            phone: '9876543210',
            email: '<EMAIL>',
            familyIncome: '50000',
            isDisabled: 'false',
            currentCourse: 'Test Course',
            courseDuration: '4 years',
            courseYear: '2nd Year',
            rollNo: 'TEST123',
            institutionName: 'Test Institution',
            institutionType: 'Government',
            institutionAddress: 'Test Institution Address, Building No 789, Campus Area, Test City, Test State, PIN 123456',
            institutionPhone: '9876543210',
            institutionEmail: '<EMAIL>',
            amountFigures: '10000',
            amountWords: 'Ten Thousand',
            place: 'Test Place',
            date: '2025-06-14'
        };

        Object.keys(testData).forEach(key => {
            const field = document.querySelector(`[name="${key}"]`);
            if (field) {
                field.value = testData[key];
                console.log(`✅ Set ${key}: ${testData[key]}`);
            } else {
                console.log(`❌ Field not found: ${key}`);
            }
        });
    };

    // Enhanced file upload functionality
    function initializeFileUploads() {
        const fileInputs = document.querySelectorAll('input[type="file"]');

        fileInputs.forEach(input => {
            input.addEventListener('change', function(e) {
                handleFileSelection(this);
            });
        });
    }

    function handleFileSelection(input) {
        const files = input.files;
        const inputId = input.id;
        const container = document.getElementById(`${inputId}-container`);
        const selectedDiv = document.getElementById(`${inputId}-selected`);

        if (files.length > 0) {
            // Show selected files
            displaySelectedFiles(files, inputId, container, selectedDiv);
        } else {
            // Hide selected files display
            if (selectedDiv) {
                selectedDiv.style.display = 'none';
            }
            if (container) {
                container.classList.remove('has-file');
            }
        }
    }

    function displaySelectedFiles(files, inputId, container, selectedDiv) {
        if (!selectedDiv) return;

        // Mark container as having files
        if (container) {
            container.classList.add('has-file');
        }

        let html = '';

        if (files.length === 1) {
            const file = files[0];
            const fileSize = formatFileSize(file.size);
            const fileIcon = getFileIcon(file.type);

            html = `
                <div class="file-selected-info">
                    <div class="file-name">
                        <i class="${fileIcon}"></i>
                        <span>${file.name}</span>
                        <span class="file-size">(${fileSize})</span>
                    </div>
                    <div class="file-actions">
                        <button type="button" class="btn-change" onclick="changeFile('${inputId}')">
                            <i class="fas fa-edit"></i> Change
                        </button>
                        <button type="button" class="btn-remove" onclick="removeFile('${inputId}')">
                            <i class="fas fa-trash"></i> Remove
                        </button>
                    </div>
                </div>
            `;
        } else {
            // Multiple files
            html = `
                <div class="file-selected-info">
                    <div class="file-name">
                        <i class="fas fa-files"></i>
                        <span>${files.length} files selected</span>
                    </div>
                    <div class="file-actions">
                        <button type="button" class="btn-change" onclick="changeFile('${inputId}')">
                            <i class="fas fa-edit"></i> Change
                        </button>
                        <button type="button" class="btn-remove" onclick="removeFile('${inputId}')">
                            <i class="fas fa-trash"></i> Remove All
                        </button>
                    </div>
                </div>
                <div style="margin-top: 0.5rem; font-size: 0.8rem; color: #666;">
                    ${Array.from(files).map(file => `• ${file.name} (${formatFileSize(file.size)})`).join('<br>')}
                </div>
            `;
        }

        selectedDiv.innerHTML = html;
        selectedDiv.style.display = 'block';
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function getFileIcon(fileType) {
        if (fileType.startsWith('image/')) {
            return 'fas fa-image';
        } else if (fileType === 'application/pdf') {
            return 'fas fa-file-pdf';
        } else if (fileType.includes('document') || fileType.includes('word')) {
            return 'fas fa-file-word';
        } else {
            return 'fas fa-file';
        }
    }

    // Global functions for file actions
    window.changeFile = function(inputId) {
        const input = document.getElementById(inputId);
        if (input) {
            input.click();
        }
    };

    window.removeFile = function(inputId) {
        const input = document.getElementById(inputId);
        const container = document.getElementById(`${inputId}-container`);
        const selectedDiv = document.getElementById(`${inputId}-selected`);

        if (input) {
            input.value = '';
            if (selectedDiv) {
                selectedDiv.style.display = 'none';
            }
            if (container) {
                container.classList.remove('has-file');
            }
        }
    };

    // Helper functions
    function updateProgress(stepNum) {
        const steps = document.querySelectorAll('.step');
        steps.forEach(step => {
            if (parseInt(step.dataset.step) <= parseInt(stepNum)) {
                step.classList.add('active');
            } else {
                step.classList.remove('active');
            }
        });
    }

    function validateStep(step) {
        // Get all required inputs in the current step
        const requiredInputs = step.querySelectorAll('[required]');
        let isValid = true;
        let invalidFields = [];

        requiredInputs.forEach(input => {
            const fieldName = input.name || input.id;
            const fieldValue = input.value ? input.value.trim() : '';
            const fieldType = input.type || input.tagName.toLowerCase();

            let isFieldValid = true;
            let errorMessage = '';

            // Check if field is empty
            if (!fieldValue) {
                isFieldValid = false;
                errorMessage = getFieldErrorMessage(fieldName, 'required');
            } else {
                // Additional validation based on field type
                if (fieldType === 'email' && !isValidEmail(fieldValue)) {
                    isFieldValid = false;
                    errorMessage = 'Please enter a valid email address';
                } else if (fieldType === 'tel' && !isValidPhone(fieldValue)) {
                    isFieldValid = false;
                    errorMessage = 'Please enter a valid phone number (10 digits)';
                } else if (fieldType === 'number') {
                    const num = parseFloat(fieldValue);
                    if (isNaN(num) || num < 0) {
                        isFieldValid = false;
                        errorMessage = 'Please enter a valid positive number';
                    }
                } else if (fieldType === 'textarea' && (fieldName === 'presentAddress' || fieldName === 'permanentAddress' || fieldName === 'institutionAddress')) {
                    // Address validation - must be between 10 and 500 characters
                    if (fieldValue.length < 10) {
                        isFieldValid = false;
                        errorMessage = 'Address must be at least 10 characters long';
                    } else if (fieldValue.length > 500) {
                        isFieldValid = false;
                        errorMessage = 'Address must be less than 500 characters';
                    }
                }
            }

            if (!isFieldValid) {
                isValid = false;
                invalidFields.push(fieldName);
                input.classList.add('error');

                // Add or update error message
                let errorMsg = input.nextElementSibling;
                if (!errorMsg || !errorMsg.classList.contains('error-message')) {
                    errorMsg = document.createElement('div');
                    errorMsg.classList.add('error-message');
                    errorMsg.style.cssText = `
                        color: #dc3545;
                        font-size: 0.8rem;
                        margin-top: 0.25rem;
                        font-weight: 500;
                    `;
                    input.parentNode.insertBefore(errorMsg, input.nextSibling);
                }
                errorMsg.textContent = errorMessage;
            } else {
                input.classList.remove('error');

                // Remove error message if it exists
                const errorMsg = input.nextElementSibling;
                if (errorMsg && errorMsg.classList.contains('error-message')) {
                    errorMsg.remove();
                }
            }
        });

        return isValid;
    }

    // Helper validation functions
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    function isValidPhone(phone) {
        const phoneRegex = /^[0-9]{10}$/;
        return phoneRegex.test(phone.replace(/\D/g, ''));
    }



    function getFieldErrorMessage(fieldName, errorType) {
        const fieldLabels = getFieldLabels();
        const fieldLabel = fieldLabels[fieldName] || fieldName;

        const fieldMessages = {
            'name': `Please enter your full name`,
            'gender': `Please select your gender`,
            'dob': `Please select your date of birth`,
            'age': `Please enter your age`,
            'birthplace': `Please enter your place of birth`,
            'maritalStatus': `Please select your marital status`,
            'religion': `Please enter your religion`,
            'category': `Please select your category`,
            'nationality': `Please enter your nationality`,
            'presentAddress': 'Please enter your present address',
            'presentState': 'Please enter your present state',
            'presentCountry': 'Please enter your present country',
            'permanentAddress': 'Please enter your permanent address',
            'permanentState': 'Please enter your permanent state',
            'permanentCountry': 'Please enter your permanent country',
            'phone': 'Please enter your phone number',
            'email': 'Please enter your email address',
            'familyIncome': 'Please enter your family income',
            'isDisabled': 'Please select disability status',
            'currentCourse': 'Please enter your current course',
            'courseDuration': 'Please enter course duration',
            'courseYear': 'Please enter course year',
            'rollNo': 'Please enter your roll number',
            'institutionName': 'Please enter institution name',
            'institutionType': 'Please select institution type',
            'institutionAddress': 'Please enter institution address',
            'institutionPhone': 'Please enter institution phone',
            'institutionEmail': 'Please enter institution email',
            'amountFigures': 'Please enter scholarship amount',
            'amountWords': 'Please enter amount in words',
            'place': 'Please enter declaration place',
            'date': 'Please select declaration date'
        };

        return fieldMessages[fieldName] || 'This field is required';
    }

    function saveFormData() {
        // Get all form inputs except file inputs
        const inputs = form.querySelectorAll('input:not([type="file"]), select, textarea');
        const formData = {};

        inputs.forEach(input => {
            if (input.type === 'radio' || input.type === 'checkbox') {
                if (input.checked) {
                    formData[input.name] = input.value;
                }
            } else {
                formData[input.name] = input.value;
            }
        });

        localStorage.setItem('scholarshipFormData', JSON.stringify(formData));
    }

    function loadFormData() {
        const savedData = localStorage.getItem('scholarshipFormData');
        if (savedData) {
            const formData = JSON.parse(savedData);

            // Populate form fields
            Object.keys(formData).forEach(key => {
                const input = form.querySelector(`[name="${key}"]`);
                if (input) {
                    if (input.type === 'radio' || input.type === 'checkbox') {
                        if (input.value === formData[key]) {
                            input.checked = true;
                        }
                    } else {
                        input.value = formData[key];
                    }
                }
            });

            // If disability is set to true, show the disability details
            if (isDisabledSelect && isDisabledSelect.value === 'true' && disabilityDetails) {
                disabilityDetails.style.display = 'block';
            }
        }
    }

    // Animation functions
    function initializeAnimations() {
        // Add staggered animation delays to navigation links
        const navLinks = document.querySelectorAll('.nav-links a');
        navLinks.forEach((link, index) => {
            link.style.setProperty('--i', index);
        });

        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');

                    // Add staggered animations to child elements
                    const children = entry.target.querySelectorAll('.quick-link-card, .gallery-item, .project-item');
                    children.forEach((child, index) => {
                        setTimeout(() => {
                            child.classList.add('animate-in');
                        }, index * 200);
                    });
                }
            });
        }, observerOptions);

        // Observe elements for scroll animations
        const animatedElements = document.querySelectorAll('section, .quick-link-card, .gallery-item, .project-item');
        animatedElements.forEach(el => {
            observer.observe(el);
        });

        // Add hover animations to buttons
        const buttons = document.querySelectorAll('.btn-primary, .btn-secondary, .admin-link');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.animation = 'pulse 0.6s ease-out';
            });

            button.addEventListener('mouseleave', function() {
                this.style.animation = '';
            });
        });

        // Add click animation to cards
        const cards = document.querySelectorAll('.quick-link-card, .gallery-item, .value-card, .contact-card');
        cards.forEach(card => {
            card.addEventListener('click', function() {
                this.style.animation = 'bounce 0.6s ease-out';
                setTimeout(() => {
                    this.style.animation = '';
                }, 600);
            });
        });

        // Parallax effect for hero section
        const hero = document.querySelector('.hero');
        if (hero) {
            window.addEventListener('scroll', () => {
                const scrolled = window.pageYOffset;
                const rate = scrolled * -0.5;
                hero.style.transform = `translateY(${rate}px)`;
            });
        }

        // Add more animations to various elements
        addAdvancedAnimations();

        // Add typing effect to hero text
        const heroTitle = document.querySelector('.hero h2');
        if (heroTitle) {
            const text = heroTitle.textContent;
            heroTitle.textContent = '';
            heroTitle.style.borderRight = '2px solid white';

            let i = 0;
            const typeWriter = () => {
                if (i < text.length) {
                    heroTitle.textContent += text.charAt(i);
                    i++;
                    setTimeout(typeWriter, 100);
                } else {
                    setTimeout(() => {
                        heroTitle.style.borderRight = 'none';
                    }, 1000);
                }
            };

            setTimeout(typeWriter, 1500);
        }

        // Add smooth scroll for anchor links
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        anchorLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // Advanced animations function
    function addAdvancedAnimations() {
        // Animate footer elements on scroll
        const footerSections = document.querySelectorAll('.footer-section');
        footerSections.forEach((section, index) => {
            section.style.animation = `slideInUp 0.8s ease-out ${index * 0.2}s both`;
        });

        // Add wave animation to page headers
        const pageHeaders = document.querySelectorAll('.page-header h1');
        pageHeaders.forEach(header => {
            header.addEventListener('mouseenter', function() {
                this.style.animation = 'shake 0.5s ease-out';
                setTimeout(() => {
                    this.style.animation = '';
                }, 500);
            });
        });

        // Animate contact info items
        const contactItems = document.querySelectorAll('.contact-info p');
        contactItems.forEach((item, index) => {
            item.style.animation = `slideInLeft 0.6s ease-out ${index * 0.1}s both`;
        });

        // Add ripple effect to buttons
        const allButtons = document.querySelectorAll('button, .btn-primary, .btn-secondary, .admin-link');
        allButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;

                this.style.position = 'relative';
                this.style.overflow = 'hidden';
                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // Animate form labels
        const formLabels = document.querySelectorAll('label');
        formLabels.forEach((label, index) => {
            label.style.animation = `fadeIn 0.5s ease-out ${index * 0.05}s both`;
        });

        // Add progress bar animation
        const progressSteps = document.querySelectorAll('.step');
        progressSteps.forEach((step, index) => {
            step.style.animation = `scaleIn 0.5s ease-out ${index * 0.1}s both`;
        });

        // Animate social links
        const socialLinks = document.querySelectorAll('.social-links a');
        socialLinks.forEach((link, index) => {
            link.style.animation = `rotateIn 0.6s ease-out ${index * 0.1}s both`;

            link.addEventListener('mouseenter', function() {
                this.style.animation = 'bounce 0.6s ease-out';
            });

            link.addEventListener('mouseleave', function() {
                this.style.animation = '';
            });
        });

        // Add text animation to paragraphs
        const paragraphs = document.querySelectorAll('p');
        paragraphs.forEach((p, index) => {
            if (index % 2 === 0) {
                p.style.animation = `slideInLeft 0.8s ease-out ${index * 0.1}s both`;
            } else {
                p.style.animation = `slideInRight 0.8s ease-out ${index * 0.1}s both`;
            }
        });

        // Animate list items
        const listItems = document.querySelectorAll('li');
        listItems.forEach((item, index) => {
            item.style.animation = `slideInUp 0.5s ease-out ${index * 0.05}s both`;
        });

        // Add hover effects to images (excluding gallery images)
        const images = document.querySelectorAll('img:not(.logo):not(.gallery-image)');
        images.forEach(img => {
            img.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
                this.style.transition = 'transform 0.3s ease-out';
            });

            img.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Animate table rows
        const tableRows = document.querySelectorAll('tr');
        tableRows.forEach((row, index) => {
            row.style.animation = `slideInLeft 0.5s ease-out ${index * 0.1}s both`;
        });

        // Add floating animation to icons
        const icons = document.querySelectorAll('i');
        icons.forEach((icon, index) => {
            setTimeout(() => {
                icon.style.animation = 'float 3s ease-in-out infinite';
                icon.style.animationDelay = `${index * 0.2}s`;
            }, 2000);
        });

        // Animate value cards
        const valueCards = document.querySelectorAll('.value-card');
        valueCards.forEach((card, index) => {
            card.style.animation = `flipIn 0.8s ease-out ${index * 0.2}s both`;
        });

        // Add scroll-triggered number counting animation
        const numbers = document.querySelectorAll('.impact-number');
        numbers.forEach(number => {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const target = parseInt(entry.target.textContent);
                        let current = 0;
                        const increment = target / 50;
                        const timer = setInterval(() => {
                            current += increment;
                            if (current >= target) {
                                current = target;
                                clearInterval(timer);
                            }
                            entry.target.textContent = Math.floor(current);
                        }, 50);
                        observer.unobserve(entry.target);
                    }
                });
            });
            observer.observe(number);
        });

        // Add typewriter effect to taglines
        const taglines = document.querySelectorAll('.tagline');
        taglines.forEach((tagline, index) => {
            setTimeout(() => {
                const text = tagline.textContent;
                tagline.textContent = '';
                let i = 0;
                const typeWriter = () => {
                    if (i < text.length) {
                        tagline.textContent += text.charAt(i);
                        i++;
                        setTimeout(typeWriter, 50);
                    }
                };
                typeWriter();
            }, 1000 + index * 500);
        });
    }

    // Authentication-based navigation update function
    function updateNavigationBasedOnAuth() {
        const candidateLoggedIn = localStorage.getItem('candidateLoggedIn');
        const adminLink = document.querySelector('.admin-link');

        if (candidateLoggedIn && adminLink) {
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');

            // Update admin link to show user dashboard
            adminLink.href = 'candidate-dashboard.html';
            adminLink.innerHTML = `<i class="fas fa-user"></i> ${currentUser.name || 'Dashboard'}`;

            // Always add logout functionality when user is logged in
            const logoutLink = document.createElement('a');
            logoutLink.href = '#';
            logoutLink.className = 'admin-link';
            logoutLink.style.marginLeft = '0.5rem';
            logoutLink.innerHTML = '<i class="fas fa-sign-out-alt"></i> Logout';
            logoutLink.addEventListener('click', async function(e) {
                e.preventDefault();
                if (confirm('Are you sure you want to logout?')) {
                    try {
                        // Get auth token
                        const authToken = localStorage.getItem('authToken');

                        // Call backend logout endpoint if token exists
                        if (authToken) {
                            await apiCall('/api/auth/logout', {
                                method: 'POST',
                                headers: {
                                    'Authorization': `Bearer ${authToken}`
                                }
                            });
                        }
                    } catch (error) {
                        console.error('Logout API call failed:', error);
                        // Continue with logout even if API call fails
                    }

                    // Clear all authentication data
                    localStorage.removeItem('candidateLoggedIn');
                    localStorage.removeItem('currentUser');
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('refreshToken');

                    // Redirect to home page
                    window.location.href = 'index.html';
                }
            });

            adminLink.parentNode.appendChild(logoutLink);
        }
    }
});

// Global logout function
window.performLogout = async function(redirectUrl = 'index.html') {
    try {
        // Get auth token
        const authToken = localStorage.getItem('authToken') || localStorage.getItem('adminAuthToken');

        // Call backend logout endpoint if token exists
        if (authToken) {
            await fetch('http://192.168.0.73:3000/api/auth/logout', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                }
            });
        }
    } catch (error) {
        console.error('Logout API call failed:', error);
        // Continue with logout even if API call fails
    }

    // Clear all authentication data
    localStorage.removeItem('candidateLoggedIn');
    localStorage.removeItem('adminLoggedIn');
    localStorage.removeItem('currentUser');
    localStorage.removeItem('authToken');
    localStorage.removeItem('adminAuthToken');
    localStorage.removeItem('refreshToken');

    // Redirect to specified page
    window.location.href = redirectUrl;
};
