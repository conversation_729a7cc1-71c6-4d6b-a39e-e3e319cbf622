<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Admin Dashboard</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Dashboard Styles */
        .dashboard-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 180px);
        }

        /* Sidebar Styles */
        .sidebar {
            background-color: #2c3e50;
            color: white;
            padding: 2rem 0;
            position: relative;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            padding: 0;
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: #34495e;
        }

        .sidebar-menu i {
            margin-right: 1rem;
            width: 20px;
            text-align: center;
        }

        .user-info {
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #34495e;
        }

        .user-name {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .user-role {
            font-size: 0.9rem;
            color: #bdc3c7;
        }

        /* Logout Button */
        .logout-button {
            margin-top: auto;
            padding: 1rem 2rem;
            background-color: #c0392b;
            color: white;
            border: none;
            cursor: pointer;
            width: 100%;
            text-align: left;
            display: flex;
            align-items: center;
        }

        .logout-button i {
            margin-right: 1rem;
        }

        /* Main Content */
        .main-content {
            padding: 2rem;
            background-color: #f8f9fa;
            min-height: calc(100vh - 180px);
            overflow-y: auto;
        }

        /* Scrollbar Styling */
        .main-content::-webkit-scrollbar {
            width: 8px;
        }

        .main-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .main-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .main-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .page-title {
            margin-bottom: 2rem;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .page-title i {
            color: #6c757d;
        }

        /* Settings Sections */
        .settings-grid {
            display: grid;
            gap: 2rem;
        }

        .settings-section {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #495057;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 1rem;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .btn-save, .btn-reset {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            margin-right: 1rem;
        }

        .btn-save {
            background-color: #28a745;
            color: white;
        }

        .btn-reset {
            background-color: #6c757d;
            color: white;
        }

        .btn-save:hover { background-color: #1e7e34; }
        .btn-reset:hover { background-color: #545b62; }

        /* Toggle Switch */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #28a745;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        /* Alert Messages */
        .alert {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            display: none;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        /* File Upload */
        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 4px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .file-upload:hover {
            border-color: #007bff;
        }

        .file-upload input {
            display: none;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo-container">
                <img src="../images/awt-logo.png" alt="Access Welfare Trust Logo" class="logo">
                <div>
                    <h1>Access Welfare Trust</h1>
                    <p class="tagline">Connect, Empower & Sustain</p>
                </div>
            </div>
            <div class="header-right">
                <a href="dashboard.html" class="admin-link active">Admin Dashboard</a>
            </div>
        </div>
    </header>

    <div class="dashboard-container">
        <aside class="sidebar">
            <div class="user-info">
                <div class="user-name">Admin User</div>
                <div class="user-role">Administrator</div>
            </div>

            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="applications.html"><i class="fas fa-file-alt"></i> Applications</a></li>
                <li><a href="approved.html"><i class="fas fa-check-circle"></i> Approved</a></li>
                <li><a href="rejected.html"><i class="fas fa-times-circle"></i> Rejected</a></li>
                <li><a href="applicants.html"><i class="fas fa-users"></i> Applicants</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> Reports</a></li>
                <li><a href="settings.html" class="active"><i class="fas fa-cog"></i> Settings</a></li>
            </ul>

            <button id="logoutButton" class="logout-button">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </aside>

        <main class="main-content">
            <h1 class="page-title">
                <i class="fas fa-cog"></i>
                System Settings
            </h1>

            <div class="settings-grid">
                <!-- General Settings -->
                <div class="settings-section">
                    <h2 class="section-title">
                        <i class="fas fa-sliders-h"></i>
                        General Settings
                    </h2>
                    
                    <div id="generalAlert" class="alert"></div>
                    
                    <form id="generalSettingsForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="organizationName">Organization Name</label>
                                <input type="text" id="organizationName" value="Access Welfare Trust">
                            </div>
                            <div class="form-group">
                                <label for="contactEmail">Contact Email</label>
                                <input type="email" id="contactEmail" value="<EMAIL>">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="contactPhone">Contact Phone</label>
                                <input type="tel" id="contactPhone" value="+91 9876543210">
                            </div>
                            <div class="form-group">
                                <label for="website">Website URL</label>
                                <input type="url" id="website" value="https://awt.org.in">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="address">Address</label>
                            <textarea id="address" placeholder="Enter organization address">123 Main Street, City, State, PIN - 123456</textarea>
                        </div>
                        
                        <button type="submit" class="btn-save">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                        <button type="button" class="btn-reset" onclick="resetForm('generalSettingsForm')">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                    </form>
                </div>

                <!-- Application Settings -->
                <div class="settings-section">
                    <h2 class="section-title">
                        <i class="fas fa-file-alt"></i>
                        Application Settings
                    </h2>
                    
                    <div id="applicationAlert" class="alert"></div>
                    
                    <form id="applicationSettingsForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="maxScholarshipAmount">Maximum Scholarship Amount (₹)</label>
                                <input type="number" id="maxScholarshipAmount" value="100000">
                            </div>
                            <div class="form-group">
                                <label for="minScholarshipAmount">Minimum Scholarship Amount (₹)</label>
                                <input type="number" id="minScholarshipAmount" value="5000">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="applicationDeadline">Application Deadline</label>
                                <input type="date" id="applicationDeadline">
                            </div>
                            <div class="form-group">
                                <label for="maxFileSize">Max File Size (MB)</label>
                                <input type="number" id="maxFileSize" value="5">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="autoApproval"> Enable Auto-Approval for Eligible Applications
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="emailNotifications" checked> Send Email Notifications
                            </label>
                        </div>

                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="formAvailable" checked> Application Form Available for Submissions
                            </label>
                            <small style="color: #666; display: block; margin-top: 5px;">
                                When disabled, users cannot submit new applications. Existing applications can still be edited if rejected.
                            </small>
                        </div>

                        <button type="submit" class="btn-save">
                            <i class="fas fa-save"></i> Save Settings
                        </button>
                        <button type="button" class="btn-reset" onclick="resetForm('applicationSettingsForm')">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                    </form>
                </div>

                <!-- Security Settings -->
                <div class="settings-section">
                    <h2 class="section-title">
                        <i class="fas fa-shield-alt"></i>
                        Security Settings
                    </h2>
                    
                    <div id="securityAlert" class="alert"></div>
                    
                    <form id="securitySettingsForm">
                        <div class="form-group">
                            <label for="sessionTimeout">Session Timeout (minutes)</label>
                            <input type="number" id="sessionTimeout" value="60" min="15" max="480">
                        </div>
                        
                        <div class="form-group">
                            <label for="passwordPolicy">Password Policy</label>
                            <select id="passwordPolicy">
                                <option value="basic">Basic (8+ characters)</option>
                                <option value="medium" selected>Medium (8+ chars, numbers, symbols)</option>
                                <option value="strong">Strong (12+ chars, mixed case, numbers, symbols)</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="twoFactorAuth"> Enable Two-Factor Authentication
                            </label>
                        </div>
                        
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="loginAttempts" checked> Limit Login Attempts
                            </label>
                        </div>
                        
                        <button type="submit" class="btn-save">
                            <i class="fas fa-save"></i> Update Security
                        </button>
                        <button type="button" class="btn-reset" onclick="resetForm('securitySettingsForm')">
                            <i class="fas fa-undo"></i> Reset
                        </button>
                    </form>
                </div>

                <!-- System Maintenance -->
                <div class="settings-section">
                    <h2 class="section-title">
                        <i class="fas fa-tools"></i>
                        System Maintenance
                    </h2>
                    
                    <div id="maintenanceAlert" class="alert"></div>
                    
                    <div class="form-group">
                        <label>Database Backup</label>
                        <p style="color: #666; margin-bottom: 1rem;">Last backup: January 15, 2025 at 2:30 AM</p>
                        <button type="button" class="btn-save" onclick="createBackup()">
                            <i class="fas fa-database"></i> Create Backup Now
                        </button>
                    </div>
                    
                    <div class="form-group">
                        <label>System Logs</label>
                        <p style="color: #666; margin-bottom: 1rem;">View and download system activity logs</p>
                        <button type="button" class="btn-save" onclick="downloadLogs()">
                            <i class="fas fa-file-download"></i> Download Logs
                        </button>
                    </div>
                    
                    <div class="form-group">
                        <label>Clear Cache</label>
                        <p style="color: #666; margin-bottom: 1rem;">Clear system cache to improve performance</p>
                        <button type="button" class="btn-reset" onclick="clearCache()">
                            <i class="fas fa-broom"></i> Clear Cache
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <footer>
        <p>&copy; 2025 Access Welfare Trust. All rights reserved.</p>
    </footer>

    <script src="../js/api-config.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check admin authentication
            const authToken = localStorage.getItem('authToken');
            if (!authToken) {
                alert('Please login as admin to access this page');
                window.location.href = '../login-signup.html';
                return;
            }

            setupEventListeners();
            loadSettings();
        });

        function setupEventListeners() {
            // Form submissions
            document.getElementById('generalSettingsForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveSettings('general');
            });

            document.getElementById('applicationSettingsForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveSettings('application');
            });

            document.getElementById('securitySettingsForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveSettings('security');
            });

            // Logout functionality
            const logoutButton = document.getElementById('logoutButton');
            if (logoutButton) {
                logoutButton.addEventListener('click', async function() {
                    if (confirm('Are you sure you want to logout?')) {
                        try {
                            const authToken = localStorage.getItem('authToken');
                            if (authToken) {
                                await fetch('http://localhost:3000/api/auth/logout', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': `Bearer ${authToken}`
                                    }
                                });
                            }
                        } catch (error) {
                            console.error('Logout API call failed:', error);
                        }

                        localStorage.removeItem('authToken');
                        localStorage.removeItem('adminLoggedIn');
                        window.location.href = '../login-signup.html';
                    }
                });
            }
        }

        async function loadSettings() {
            try {
                const authToken = localStorage.getItem('authToken');
                const response = await apiCall('/api/admin/settings', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    const settings = result.data || {};

                    // Load form availability setting
                    document.getElementById('formAvailable').checked = settings.form_available !== false;

                    // Load other settings if available
                    if (settings.max_scholarship_amount) {
                        document.getElementById('maxScholarshipAmount').value = settings.max_scholarship_amount;
                    }
                    if (settings.min_scholarship_amount) {
                        document.getElementById('minScholarshipAmount').value = settings.min_scholarship_amount;
                    }
                    if (settings.max_file_size) {
                        document.getElementById('maxFileSize').value = settings.max_file_size;
                    }
                    if (settings.application_deadline) {
                        document.getElementById('applicationDeadline').value = settings.application_deadline.split('T')[0];
                    }
                } else {
                    console.log('Settings not found, using defaults');
                }
            } catch (error) {
                console.error('Error loading settings:', error);
            }

            // Set default application deadline to 3 months from now if not set
            if (!document.getElementById('applicationDeadline').value) {
                const deadline = new Date();
                deadline.setMonth(deadline.getMonth() + 3);
                document.getElementById('applicationDeadline').value = deadline.toISOString().split('T')[0];
            }
        }

        async function saveSettings(section) {
            const alertElement = document.getElementById(section + 'Alert');

            try {
                const authToken = localStorage.getItem('authToken');
                let settingsData = {};

                if (section === 'application') {
                    settingsData = {
                        form_available: document.getElementById('formAvailable').checked,
                        max_scholarship_amount: parseInt(document.getElementById('maxScholarshipAmount').value),
                        min_scholarship_amount: parseInt(document.getElementById('minScholarshipAmount').value),
                        max_file_size: parseInt(document.getElementById('maxFileSize').value),
                        application_deadline: document.getElementById('applicationDeadline').value,
                        auto_approval: document.getElementById('autoApproval').checked,
                        email_notifications: document.getElementById('emailNotifications').checked
                    };
                } else if (section === 'general') {
                    settingsData = {
                        organization_name: document.getElementById('organizationName').value,
                        contact_email: document.getElementById('contactEmail').value,
                        contact_phone: document.getElementById('contactPhone').value,
                        website: document.getElementById('website').value,
                        address: document.getElementById('address').value
                    };
                } else if (section === 'security') {
                    settingsData = {
                        session_timeout: parseInt(document.getElementById('sessionTimeout').value),
                        password_policy: document.getElementById('passwordPolicy').value,
                        two_factor_auth: document.getElementById('twoFactorAuth').checked,
                        login_attempts_limit: document.getElementById('loginAttempts').checked
                    };
                }

                const response = await apiCall('/api/admin/settings', {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(settingsData)
                });

                if (response.ok) {
                    showAlert(alertElement, 'success', `${section.charAt(0).toUpperCase() + section.slice(1)} settings saved successfully!`);

                    // If form availability was changed, show additional message
                    if (section === 'application' && 'form_available' in settingsData) {
                        const formStatus = settingsData.form_available ? 'enabled' : 'disabled';
                        showAlert(alertElement, 'info', `Application form is now ${formStatus} for new submissions.`);
                    }
                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Failed to save settings');
                }
            } catch (error) {
                console.error('Error saving settings:', error);
                showAlert(alertElement, 'error', `Error saving settings: ${error.message}`);
            }
        }

        function resetForm(formId) {
            if (confirm('Are you sure you want to reset all changes?')) {
                document.getElementById(formId).reset();
                loadSettings();
            }
        }

        function showAlert(element, type, message) {
            element.className = `alert alert-${type}`;
            element.textContent = message;
            element.style.display = 'block';
            
            setTimeout(() => {
                element.style.display = 'none';
            }, 3000);
        }

        function createBackup() {
            const alertElement = document.getElementById('maintenanceAlert');
            showAlert(alertElement, 'success', 'Database backup created successfully!');
        }

        function downloadLogs() {
            // Create sample log content
            const logContent = `[2025-01-15 10:30:00] INFO: User login successful - <EMAIL>
[2025-01-15 10:31:15] INFO: Application submitted - ID: AWT-2025-001
[2025-01-15 10:32:30] INFO: Application approved - ID: AWT-2025-001
[2025-01-15 10:33:45] WARNING: Failed login attempt - <EMAIL>`;

            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system_logs_${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        function clearCache() {
            const alertElement = document.getElementById('maintenanceAlert');
            showAlert(alertElement, 'success', 'System cache cleared successfully!');
        }
    </script>
</body>
</html>
