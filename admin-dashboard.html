<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Access Welfare Trust</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .stat-label {
            color: #666;
            font-size: 1.1rem;
        }
        .applications-section {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .filters {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        .filter-select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        .applications-table {
            width: 100%;
            border-collapse: collapse;
        }
        .applications-table th,
        .applications-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        .applications-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        .applications-table tbody tr:hover {
            background: #f8f9fa;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        .status-draft { background: #fff3cd; color: #856404; }
        .status-submitted { background: #d4edda; color: #155724; }
        .status-under-review { background: #d1ecf1; color: #0c5460; }
        .status-approved { background: #d4edda; color: #155724; }
        .status-rejected { background: #f8d7da; color: #721c24; }
        .action-buttons {
            display: flex;
            gap: 8px;
        }
        .btn-action {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.85rem;
            text-decoration: none;
            display: inline-block;
        }
        .btn-view { background: #007bff; color: white; }
        .btn-approve { background: #28a745; color: white; }
        .btn-reject { background: #dc3545; color: white; }
        .btn-action:hover { opacity: 0.8; }
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            gap: 10px;
        }
        .page-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        .page-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        .search-box {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 250px;
        }
    </style>
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h2>Access Welfare Trust - Admin</h2>
                </div>
                <ul class="nav-links">
                    <li><a href="admin-dashboard.html">Dashboard</a></li>
                    <li><a href="index.html">Public Site</a></li>
                    <li><a href="#" onclick="logout()">Logout</a></li>
                </ul>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <div class="admin-container">
            <div class="admin-header">
                <h1>🏛️ Admin Dashboard</h1>
                <p>Manage scholarship applications and review submissions</p>
                <!-- Debug Test Button -->
                <button onclick="testDownloadFunction()" style="background: red; color: white; padding: 10px; margin: 10px; border: none; border-radius: 5px;">
                    🔧 Test Download Function
                </button>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalApplications">-</div>
                    <div class="stat-label">Total Applications</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="pendingApplications">-</div>
                    <div class="stat-label">Pending Review</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="approvedApplications">-</div>
                    <div class="stat-label">Approved</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="rejectedApplications">-</div>
                    <div class="stat-label">Rejected</div>
                </div>
            </div>

            <!-- Applications Section -->
            <div class="applications-section">
                <div class="section-header">
                    <h2>📋 Applications Management</h2>
                    <div class="filters">
                        <input type="text" class="search-box" id="searchBox" placeholder="Search by name, email, or ID...">
                        <select class="filter-select" id="statusFilter">
                            <option value="">All Status</option>
                            <option value="draft">Draft</option>
                            <option value="submitted">Submitted</option>
                            <option value="under_review">Under Review</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                        </select>
                        <button class="btn-action btn-view" onclick="refreshApplications()">🔄 Refresh</button>
                    </div>
                </div>

                <div id="applicationsContainer">
                    <div class="loading">
                        <p>Loading applications...</p>
                    </div>
                </div>

                <div class="pagination" id="pagination" style="display: none;">
                    <!-- Pagination will be inserted here -->
                </div>
            </div>
        </div>
    </main>

    <!-- Application Details Modal -->
    <div id="applicationModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="background: white; margin: 50px auto; padding: 30px; width: 90%; max-width: 800px; border-radius: 10px; max-height: 80vh; overflow-y: auto;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>Application Details</h2>
                <button onclick="closeModal()" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
            </div>
            <div id="applicationDetails">
                <!-- Application details will be loaded here -->
            </div>
        </div>
    </div>

    <script src="js/api-config.js"></script>
    <script>
        let currentPage = 1;
        let totalPages = 1;
        let applications = [];

        document.addEventListener('DOMContentLoaded', function() {
            // Check admin authentication
            const authToken = localStorage.getItem('authToken');
            if (!authToken) {
                alert('Please login as admin to access this page');
                window.location.href = 'login-signup.html';
                return;
            }

            loadDashboardData();
            setupEventListeners();
        });

        function setupEventListeners() {
            document.getElementById('searchBox').addEventListener('input', debounce(filterApplications, 300));
            document.getElementById('statusFilter').addEventListener('change', filterApplications);
        }

        async function loadDashboardData() {
            try {
                await Promise.all([
                    loadStatistics(),
                    loadApplications()
                ]);
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showError('Failed to load dashboard data');
            }
        }

        async function loadStatistics() {
            try {
                const authToken = localStorage.getItem('authToken');
                const response = await apiCall('/api/admin/dashboard', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    const stats = result.data.statistics;
                    updateStatistics({
                        total: stats.total_applications || 0,
                        pending: (stats.submitted_applications || 0) + (stats.under_review_applications || 0),
                        approved: stats.approved_applications || 0,
                        rejected: stats.rejected_applications || 0
                    });
                } else {
                    // Fallback: calculate from applications
                    calculateStatisticsFromApplications();
                }
            } catch (error) {
                console.error('Error loading statistics:', error);
                calculateStatisticsFromApplications();
            }
        }

        function updateStatistics(stats) {
            document.getElementById('totalApplications').textContent = stats.total || 0;
            document.getElementById('pendingApplications').textContent = stats.pending || 0;
            document.getElementById('approvedApplications').textContent = stats.approved || 0;
            document.getElementById('rejectedApplications').textContent = stats.rejected || 0;
        }

        function calculateStatisticsFromApplications() {
            const total = applications.length;
            const pending = applications.filter(app => app.status === 'submitted' || app.status === 'under_review').length;
            const approved = applications.filter(app => app.status === 'approved').length;
            const rejected = applications.filter(app => app.status === 'rejected').length;

            updateStatistics({ total, pending, approved, rejected });
        }

        async function loadApplications() {
            try {
                const authToken = localStorage.getItem('authToken');
                const response = await apiCall('/api/admin/applications', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    applications = result.data.applications || [];
                    displayApplications(applications);
                    calculateStatisticsFromApplications();
                } else {
                    throw new Error('Failed to load applications');
                }
            } catch (error) {
                console.error('Error loading applications:', error);
                document.getElementById('applicationsContainer').innerHTML =
                    '<div class="no-data"><p>Failed to load applications. Please try again.</p></div>';
            }
        }

        function displayApplications(apps) {
            const container = document.getElementById('applicationsContainer');
            
            if (apps.length === 0) {
                container.innerHTML = '<div class="no-data"><p>No applications found.</p></div>';
                return;
            }

            const table = `
                <table class="applications-table">
                    <thead>
                        <tr>
                            <th>Application ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Course</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Submitted</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${apps.map(app => `
                            <tr>
                                <td><strong>${app.application_id || 'N/A'}</strong></td>
                                <td>${app.name || 'N/A'}</td>
                                <td>${app.email || 'N/A'}</td>
                                <td>${app.current_course || 'N/A'}</td>
                                <td>₹${app.scholarship_amount_figures ? app.scholarship_amount_figures.toLocaleString() : 'N/A'}</td>
                                <td><span class="status-badge status-${app.status || 'draft'}">${(app.status || 'draft').replace('_', ' ')}</span></td>
                                <td>${app.created_at ? new Date(app.created_at).toLocaleDateString() : 'N/A'}</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-action btn-view" onclick="viewApplication(${app.id})">View</button>
                                        ${app.status !== 'approved' ? `<button class="btn-action btn-approve" onclick="updateStatus(${app.id}, 'approved')">Approve</button>` : ''}
                                        ${app.status !== 'rejected' ? `<button class="btn-action btn-reject" onclick="updateStatus(${app.id}, 'rejected')">Reject</button>` : ''}
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            container.innerHTML = table;
        }

        function filterApplications() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;

            let filtered = applications.filter(app => {
                const matchesSearch = !searchTerm || 
                    (app.name && app.name.toLowerCase().includes(searchTerm)) ||
                    (app.email && app.email.toLowerCase().includes(searchTerm)) ||
                    (app.application_id && app.application_id.toLowerCase().includes(searchTerm));

                const matchesStatus = !statusFilter || app.status === statusFilter;

                return matchesSearch && matchesStatus;
            });

            displayApplications(filtered);
        }

        async function viewApplication(applicationId) {
            try {
                const authToken = localStorage.getItem('authToken');
                const response = await apiCall(`/api/admin/applications/${applicationId}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showApplicationDetails(result.data.application);
                } else {
                    alert('Failed to load application details');
                }
            } catch (error) {
                console.error('Error loading application details:', error);
                alert('Error loading application details');
            }
        }

        let currentApplicationId = null;

        function showApplicationDetails(app) {
            console.log('🎯 showApplicationDetails called with app:', app);
            currentApplicationId = app.id;
            console.log('📋 Set currentApplicationId to:', currentApplicationId);
            const modal = document.getElementById('applicationModal');
            const details = document.getElementById('applicationDetails');
            console.log('🔍 Modal element:', modal);
            console.log('🔍 Details element:', details);

            details.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h3>Personal Information</h3>
                        <p><strong>Name:</strong> ${app.name || 'N/A'}</p>
                        <p><strong>Email:</strong> ${app.email || 'N/A'}</p>
                        <p><strong>Phone:</strong> ${app.phone || 'N/A'}</p>
                        <p><strong>Date of Birth:</strong> ${app.date_of_birth || 'N/A'}</p>
                        <p><strong>Age:</strong> ${app.age || 'N/A'}</p>
                        <p><strong>Gender:</strong> ${app.gender || 'N/A'}</p>
                        <p><strong>Category:</strong> ${app.category || 'N/A'}</p>
                    </div>
                    <div>
                        <h3>Educational Information</h3>
                        <p><strong>Course:</strong> ${app.current_course || 'N/A'}</p>
                        <p><strong>Institution:</strong> ${app.institution_name || 'N/A'}</p>
                        <p><strong>Roll Number:</strong> ${app.roll_number || 'N/A'}</p>
                        <p><strong>Course Year:</strong> ${app.course_year || 'N/A'}</p>
                        <p><strong>Institution Type:</strong> ${app.institution_type || 'N/A'}</p>
                    </div>
                </div>
                <div style="margin-top: 20px;">
                    <h3>Scholarship Information</h3>
                    <p><strong>Amount Requested:</strong> ₹${app.scholarship_amount_figures ? app.scholarship_amount_figures.toLocaleString() : 'N/A'}</p>
                    <p><strong>Amount in Words:</strong> ${app.scholarship_amount_words || 'N/A'}</p>
                    <p><strong>Family Income:</strong> ₹${app.family_total_income ? app.family_total_income.toLocaleString() : 'N/A'}</p>
                </div>
                <div style="margin-top: 20px; text-align: center;">
                    <button class="btn-action" style="background-color: #007bff !important; color: white !important; margin-right: 10px; padding: 10px 15px; border: 2px solid #007bff; display: inline-block !important; visibility: visible !important;" onclick="downloadApplicationZip();">
                        <i class="fas fa-download"></i> Download All Documents
                    </button>
                    <button class="btn-action btn-approve" onclick="updateStatus(${app.id}, 'approved'); closeModal();">Approve Application</button>
                    <button class="btn-action btn-reject" onclick="updateStatus(${app.id}, 'rejected'); closeModal();">Reject Application</button>
                </div>
            `;

            console.log('📄 Modal content set, showing modal');
            console.log('🔍 Modal HTML content:', details.innerHTML);

            // Check if download button exists
            const downloadBtn = modal.querySelector('button[onclick*="downloadApplicationZip"]');
            console.log('🔽 Download button found:', !!downloadBtn);
            if (downloadBtn) {
                console.log('🔽 Download button style:', downloadBtn.style.cssText);
                console.log('🔽 Download button visible:', downloadBtn.offsetWidth > 0 && downloadBtn.offsetHeight > 0);
            }

            modal.style.display = 'block';
            console.log('✅ Modal displayed');
        }

        function closeModal() {
            console.log('❌ Closing modal, resetting currentApplicationId');
            document.getElementById('applicationModal').style.display = 'none';
            currentApplicationId = null;
            console.log('✅ Modal closed, currentApplicationId reset');
        }

        async function updateStatus(applicationId, newStatus) {
            let adminNotes;
            do {
                adminNotes = prompt(`Please enter review notes for this ${newStatus} decision (minimum 10 characters):`, '');
                if (adminNotes === null) return; // User cancelled

                if (adminNotes.trim().length < 10) {
                    alert('Admin comments must be at least 10 characters long. Please provide detailed feedback.');
                    continue;
                }
                break;
            } while (true);

            if (!confirm(`Are you sure you want to ${newStatus} this application?`)) {
                return;
            }

            try {
                const authToken = localStorage.getItem('authToken');
                const response = await apiCall(`/api/admin/applications/${applicationId}/review`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        status: newStatus,
                        admin_comments: adminNotes
                    })
                });

                if (response.ok) {
                    alert(`Application ${newStatus} successfully!`);
                    refreshApplications();
                } else {
                    alert('Failed to update application status');
                }
            } catch (error) {
                console.error('Error updating status:', error);
                alert('Error updating application status');
            }
        }

        function refreshApplications() {
            loadApplications();
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                localStorage.removeItem('authToken');
                localStorage.removeItem('currentUser');
                window.location.href = 'login-signup.html';
            }
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        function testDownloadFunction() {
            console.log('🔧 Test download function called');
            currentApplicationId = 1; // Set test ID
            downloadApplicationZip();
        }

        function downloadApplicationZip() {
            console.log('🔽 Download button clicked!');
            console.log('📋 Current Application ID:', currentApplicationId);

            if (!currentApplicationId) {
                alert('No application selected');
                return;
            }

            const authToken = localStorage.getItem('authToken');
            console.log('🔑 Auth token exists:', !!authToken);

            // Create download link for zip file
            const link = document.createElement('a');
            link.href = `http://localhost:3000/api/admin/applications/${currentApplicationId}/download-zip`;
            link.style.display = 'none';

            // Set authorization header by adding token to URL
            link.href += `?token=${authToken}`;

            console.log('🔗 Download URL:', link.href);

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log('✅ Download initiated');
        }

        function showError(message) {
            alert(message); // Simple error handling - can be enhanced with better UI
        }
    </script>
</body>
</html>
