const { body, param, query, validationResult } = require('express-validator');
const fs = require('fs');
const path = require('path');

// Load validation rules from JSON file
let validationRules = {};
try {
    const rulesPath = path.join(__dirname, '../../form_field_validation.json');
    const rulesData = fs.readFileSync(rulesPath, 'utf8');
    const rulesJson = JSON.parse(rulesData);

    // Convert array to object for easier lookup
    rulesJson.fields.forEach(field => {
        validationRules[field.name] = field.required;
    });

    console.log('✅ Validation rules loaded successfully');
    console.log(`📋 Total fields: ${Object.keys(validationRules).length}`);
} catch (error) {
    console.error('❌ Failed to load validation rules:', error.message);
    // Fallback to basic validation if file not found
    validationRules = {};
}

// Handle validation errors
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        console.log('❌ Validation failed for request:', req.method, req.url);
        console.log('📋 Request body keys:', Object.keys(req.body));
        console.log('📋 Validation errors:');
        errors.array().forEach(error => {
            console.log(`   - ${error.param}: ${error.msg} (value: ${error.value})`);
        });

        return res.status(400).json({
            success: false,
            error: 'Validation failed',
            details: errors.array()
        });
    }
    console.log('✅ Validation passed for:', req.method, req.url);
    next();
};

// User registration validation
const validateRegistration = [
    body('name')
        .trim()
        .isLength({ min: 2, max: 255 })
        .withMessage('Name must be between 2 and 255 characters'),
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    body('phone')
        .optional()
        .matches(/^[\+]?[1-9][\d]{0,15}$/)
        .withMessage('Please provide a valid phone number'),
    body('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
    handleValidationErrors
];

// User login validation
const validateLogin = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    body('password')
        .notEmpty()
        .withMessage('Password is required'),
    handleValidationErrors
];

// Helper function to create conditional validation
const createConditionalValidation = (fieldName, condition) => {
    return body(fieldName).custom((value, { req }) => {
        // Check if the condition is met
        if (condition(req.body)) {
            // Field is required, check if it's provided and valid
            if (!value || (typeof value === 'string' && value.trim() === '')) {
                throw new Error(`${fieldName} is required when disability is enabled`);
            }
        }
        return true;
    });
};

// Dynamic application validation based on JSON rules
const validateApplication = [
    // Required fields validation
    ...Object.keys(validationRules).filter(fieldName => {
        const rule = validationRules[fieldName];
        return rule === true; // Only truly required fields
    }).map(fieldName => {
        // Create specific validation for each field type
        switch (fieldName) {
            case 'name':
                return body('name').trim().isLength({ min: 2, max: 255 }).withMessage('Name must be between 2 and 255 characters');
            case 'gender':
                return body('gender').isIn(['Male', 'Female', 'Other']).withMessage('Gender must be Male, Female, or Other');
            case 'date_of_birth':
                return body('date_of_birth').isDate().withMessage('Please provide a valid date of birth');
            case 'age':
                return body('age').isInt({ min: 1, max: 100 }).withMessage('Age must be between 1 and 100');
            case 'place_of_birth':
                return body('place_of_birth').trim().isLength({ min: 2, max: 255 }).withMessage('Place of birth is required');
            case 'marital_status':
                return body('marital_status').isIn(['Single', 'Married', 'Divorced', 'Widowed']).withMessage('Invalid marital status');
            case 'religion':
                return body('religion').trim().isLength({ min: 1, max: 100 }).withMessage('Religion is required');
            case 'category':
                return body('category').isIn(['BC', 'MBC', 'SC', 'ST', 'General']).withMessage('Invalid category');
            case 'nationality':
                return body('nationality').trim().isLength({ min: 2, max: 100 }).withMessage('Nationality is required');
            case 'present_address':
                return body('present_address').trim().isLength({ min: 10, max: 500 }).withMessage('Present address must be between 10 and 500 characters');
            case 'present_state':
                return body('present_state').trim().isLength({ min: 2, max: 100 }).withMessage('Present state is required');
            case 'present_country':
                return body('present_country').trim().isLength({ min: 2, max: 100 }).withMessage('Present country is required');
            case 'phone':
                return body('phone').isMobilePhone('any').withMessage('Please provide a valid phone number');
            case 'email':
                return body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email');
            case 'family_total_income':
                return body('family_total_income').isFloat({ min: 0 }).withMessage('Family total income must be a positive number');
            case 'is_disabled':
                return body('is_disabled').custom((value) => {
                    if (typeof value === 'boolean') return true;
                    if (value === 'true' || value === 'false') return true;
                    if (value === true || value === false) return true;
                    throw new Error('Disability status must be true, false, "true", or "false"');
                }).withMessage('Disability status must be true or false');
            case 'current_course':
                return body('current_course').trim().isLength({ min: 2, max: 255 }).withMessage('Current course is required');
            case 'course_duration':
                return body('course_duration').trim().isLength({ min: 1, max: 100 }).withMessage('Course duration is required');
            case 'course_year':
                return body('course_year').trim().isLength({ min: 1, max: 50 }).withMessage('Course year is required');
            case 'roll_number':
                return body('roll_number').trim().isLength({ min: 1, max: 100 }).withMessage('Roll number is required');
            case 'institution_name':
                return body('institution_name').trim().isLength({ min: 2, max: 255 }).withMessage('Institution name is required');
            case 'institution_type':
                return body('institution_type').isIn(['Government', 'Aided', 'Private']).withMessage('Invalid institution type');
            case 'institution_address':
                return body('institution_address').trim().isLength({ min: 10, max: 500 }).withMessage('Institution address is required');
            case 'institution_phone':
                return body('institution_phone').isMobilePhone('any').withMessage('Please provide a valid institution phone number');
            case 'institution_email':
                return body('institution_email').isEmail().normalizeEmail().withMessage('Please provide a valid institution email');
            case 'term_fees':
                return body('term_fees').trim().isLength({ min: 1, max: 100 }).withMessage('Term fees is required');
            case 'tuition_fees':
                return body('tuition_fees').isFloat({ min: 0 }).withMessage('Tuition fees must be a positive number');
            case 'other_fees':
                return body('other_fees').isFloat({ min: 0 }).withMessage('Other fees must be a positive number');
            case 'scholarship_amount_figures':
                return body('scholarship_amount_figures').isFloat({ min: 1 }).withMessage('Scholarship amount must be greater than 0');
            case 'scholarship_amount_words':
                return body('scholarship_amount_words').trim().isLength({ min: 5, max: 500 }).withMessage('Scholarship amount in words is required');
            case 'reference1_name':
                return body('reference1_name').trim().isLength({ min: 2, max: 255 }).withMessage('Reference 1 name is required');
            case 'reference1_phone':
                return body('reference1_phone').isMobilePhone('any').withMessage('Please provide a valid reference 1 phone number');
            case 'reference1_email':
                return body('reference1_email').isEmail().normalizeEmail().withMessage('Please provide a valid reference 1 email');
            case 'reference1_position':
                return body('reference1_position').trim().isLength({ min: 2, max: 255 }).withMessage('Reference 1 position is required');
            case 'reference2_name':
                return body('reference2_name').trim().isLength({ min: 2, max: 255 }).withMessage('Reference 2 name is required');
            case 'reference2_phone':
                return body('reference2_phone').isMobilePhone('any').withMessage('Please provide a valid reference 2 phone number');
            case 'reference2_email':
                return body('reference2_email').isEmail().normalizeEmail().withMessage('Please provide a valid reference 2 email');
            case 'reference2_position':
                return body('reference2_position').trim().isLength({ min: 2, max: 255 }).withMessage('Reference 2 position is required');
            case 'goals':
                return body('goals').trim().isLength({ min: 10, max: 1000 }).withMessage('Goals must be between 10 and 1000 characters');
            case 'declaration_place':
                return body('declaration_place').trim().isLength({ min: 2, max: 255 }).withMessage('Declaration place is required');
            case 'declaration_date':
                return body('declaration_date').isDate().withMessage('Please provide a valid declaration date');
            default:
                return body(fieldName).optional(); // Default for unknown required fields
        }
    }),

    // Conditional fields validation (disability-related fields)
    createConditionalValidation('disability_type', (body) => body.is_disabled === true || body.is_disabled === 'true'),
    createConditionalValidation('disability_percentage', (body) => body.is_disabled === true || body.is_disabled === 'true'),
    createConditionalValidation('disability_description', (body) => body.is_disabled === true || body.is_disabled === 'true'),
    createConditionalValidation('issuing_authority', (body) => body.is_disabled === true || body.is_disabled === 'true'),
    createConditionalValidation('certificate_number', (body) => body.is_disabled === true || body.is_disabled === 'true'),
    createConditionalValidation('issue_date', (body) => body.is_disabled === true || body.is_disabled === 'true'),

    // Conditional fields validation (permanent address - required if not same as present)
    body('permanent_address').custom((value, { req }) => {
        // If sameAsPresent is not checked, permanent address is required
        if (!req.body.sameAsPresent) {
            if (!value || value.trim() === '') {
                throw new Error('Permanent address is required when different from present address');
            }
            if (value.trim().length < 10 || value.trim().length > 500) {
                throw new Error('Permanent address must be between 10 and 500 characters');
            }
        }
        return true;
    }),
    body('permanent_state').custom((value, { req }) => {
        if (!req.body.sameAsPresent) {
            if (!value || value.trim() === '') {
                throw new Error('Permanent state is required when different from present address');
            }
        }
        return true;
    }),
    body('permanent_country').custom((value, { req }) => {
        if (!req.body.sameAsPresent) {
            if (!value || value.trim() === '') {
                throw new Error('Permanent country is required when different from present address');
            }
        }
        return true;
    }),

    // Optional fields validation (only validate format if provided)
    body('whatsapp').optional().isMobilePhone('any').withMessage('Please provide a valid WhatsApp number'),
    body('institution_website').optional().isURL().withMessage('Please provide a valid website URL'),
    body('previous_awt_scholarship').optional().trim().isLength({ max: 500 }).withMessage('Previous AWT scholarship details must not exceed 500 characters'),
    body('other_scholarships').optional().trim().isLength({ max: 500 }).withMessage('Other scholarships details must not exceed 500 characters'),
    body('applied_scholarships').optional().trim().isLength({ max: 500 }).withMessage('Applied scholarships details must not exceed 500 characters'),
    body('extracurricular').optional().trim().isLength({ max: 1000 }).withMessage('Extracurricular activities must not exceed 1000 characters'),
    body('other_info').optional().trim().isLength({ max: 1000 }).withMessage('Other information must not exceed 1000 characters'),

    handleValidationErrors
];

// File upload validation
const validateFileUpload = [
    body('file_type')
        .isIn(['photo', 'signature', 'photo_id', 'institute_id', 'address_proof',
               'income_certificate', 'disability_certificate', 'marksheets', 'fee_circular',
               'aadhaar_card', 'birth_certificate', 'community_certificate', 'course_details',
               'attendance_certificate', 'fee_receipts', 'other'])
        .withMessage('Invalid file type'),
    handleValidationErrors
];

// Admin application review validation
const validateApplicationReview = [
    body('status')
        .isIn(['approved', 'rejected'])
        .withMessage('Status must be approved or rejected'),
    body('admin_notes')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Admin notes must not exceed 1000 characters'),
    handleValidationErrors
];

// Password reset validation
const validatePasswordReset = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    handleValidationErrors
];

// New password validation
const validateNewPassword = [
    body('token')
        .notEmpty()
        .withMessage('Reset token is required'),
    body('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
    handleValidationErrors
];

// ID parameter validation
const validateId = [
    param('id')
        .isInt({ min: 1 })
        .withMessage('Invalid ID parameter'),
    handleValidationErrors
];

// Pagination validation
const validatePagination = [
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    handleValidationErrors
];

module.exports = {
    validateRegistration,
    validateLogin,
    validateApplication,
    validateFileUpload,
    validateApplicationReview,
    validatePasswordReset,
    validateNewPassword,
    validateId,
    validatePagination,
    handleValidationErrors
};
