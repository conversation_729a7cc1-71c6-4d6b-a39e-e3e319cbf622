-- Database updates for application review and resubmission system
-- Run this script to add required fields and tables

USE trust_scholarship_db;

-- Add editable field to applications table
ALTER TABLE applications 
ADD COLUMN editable BOOLEAN DEFAULT FALSE AFTER admin_notes;

-- Update status enum to include more specific states
ALTER TABLE applications 
MODIFY COLUMN status ENUM('draft', 'submitted', 'pending', 'under_review', 'approved', 'rejected', 'resubmitted') DEFAULT 'draft';

-- Rename admin_notes to admin_comments for clarity
ALTER TABLE applications 
CHANGE COLUMN admin_notes admin_comments TEXT;

-- Create settings table for system-wide configurations
CREATE TABLE IF NOT EXISTS system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    setting_type ENUM('boolean', 'string', 'number', 'json') DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_setting_key (setting_key)
);

-- Insert default settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('application_form_enabled', 'true', 'boolean', 'Controls whether users can access and submit application forms'),
('application_deadline', '2025-12-31', 'string', 'Last date for application submissions'),
('max_applications_per_user', '1', 'number', 'Maximum number of applications a user can submit'),
('notification_email', '<EMAIL>', 'string', 'Email address for system notifications'),
('system_maintenance_mode', 'false', 'boolean', 'Enable maintenance mode to restrict access')
ON DUPLICATE KEY UPDATE 
setting_value = VALUES(setting_value),
updated_at = CURRENT_TIMESTAMP;

-- Create application_history table to track status changes
CREATE TABLE IF NOT EXISTS application_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    previous_status VARCHAR(50),
    new_status VARCHAR(50) NOT NULL,
    admin_id INT,
    admin_comments TEXT,
    action_type ENUM('status_change', 'comment_added', 'resubmission', 'document_update') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_application_id (application_id),
    INDEX idx_admin_id (admin_id),
    INDEX idx_created_at (created_at)
);

-- Update existing applications to set default editable status
UPDATE applications 
SET editable = CASE 
    WHEN status = 'rejected' THEN TRUE 
    ELSE FALSE 
END;

-- Add indexes for better performance
CREATE INDEX idx_applications_status_editable ON applications(status, editable);
CREATE INDEX idx_applications_user_status ON applications(user_id, status);

-- Verify the changes
DESCRIBE applications;
DESCRIBE system_settings;
DESCRIBE application_history;

-- Show current settings
SELECT * FROM system_settings;

COMMIT;
