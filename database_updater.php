<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Updater - Trust Scholarship</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #2c3e50; color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .code { background: #f8f9fa; padding: 10px; border-left: 4px solid #007bff; margin: 10px 0; font-family: monospace; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ Database Updater - Trust Scholarship System</h1>
            <p>Update database structure to align with application form requirements</p>
        </div>

        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_db'])) {
            try {
                $pdo = new PDO("mysql:host=localhost;dbname=trust_scholarship_db", "root", "");
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                echo '<div class="status success">✅ Connected to database successfully!</div>';
                
                // Check current structure
                echo '<h3>📋 Current Database Structure</h3>';
                
                // Check file_uploads table
                $stmt = $pdo->query("SHOW COLUMNS FROM file_uploads WHERE Field = 'file_type'");
                $column = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo '<div class="info"><strong>Current file_type ENUM:</strong><br>';
                echo '<div class="code">' . htmlspecialchars($column['Type']) . '</div></div>';
                
                // Check what file types are missing
                $currentEnum = $column['Type'];
                $requiredTypes = [
                    'photo', 'marksheets', 'fee_circular', 'signature', 'photo_id', 
                    'institute_id', 'address_proof', 'aadhaar_card', 'birth_certificate', 
                    'community_certificate', 'course_details', 'income_certificate', 
                    'attendance_certificate', 'fee_receipts', 'disability_certificate', 'other'
                ];
                
                $missingTypes = [];
                foreach ($requiredTypes as $type) {
                    if (strpos($currentEnum, "'$type'") === false) {
                        $missingTypes[] = $type;
                    }
                }
                
                if (empty($missingTypes)) {
                    echo '<div class="status success">✅ All required file types are already present!</div>';
                } else {
                    echo '<div class="status warning">⚠️ Missing file types: ' . implode(', ', $missingTypes) . '</div>';
                    
                    // Update the ENUM
                    echo '<h3>🔄 Updating Database...</h3>';
                    
                    $sql = "ALTER TABLE file_uploads 
                            MODIFY COLUMN file_type ENUM(
                                'photo',
                                'marksheets',
                                'fee_circular',
                                'signature',
                                'photo_id',
                                'institute_id',
                                'address_proof',
                                'aadhaar_card',
                                'birth_certificate',
                                'community_certificate',
                                'course_details',
                                'income_certificate',
                                'attendance_certificate',
                                'fee_receipts',
                                'disability_certificate',
                                'other'
                            ) NOT NULL";
                    
                    $pdo->exec($sql);
                    echo '<div class="status success">✅ File_uploads table updated successfully!</div>';
                }
                
                // Verify the update
                echo '<h3>✅ Updated Database Structure</h3>';
                $stmt = $pdo->query("SHOW COLUMNS FROM file_uploads WHERE Field = 'file_type'");
                $column = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo '<div class="info"><strong>Updated file_type ENUM:</strong><br>';
                echo '<div class="code">' . htmlspecialchars($column['Type']) . '</div></div>';
                
                // Show applications table info
                echo '<h3>📊 Applications Table Information</h3>';
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'trust_scholarship_db' AND TABLE_NAME = 'applications'");
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                echo '<div class="info">Applications table has <strong>' . $result['count'] . '</strong> columns</div>';
                
                // Show current file uploads
                echo '<h3>📁 Current File Uploads in Database</h3>';
                $stmt = $pdo->query("SELECT file_type, COUNT(*) as count FROM file_uploads GROUP BY file_type ORDER BY file_type");
                $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (empty($results)) {
                    echo '<div class="info">No files uploaded yet.</div>';
                } else {
                    echo '<table>';
                    echo '<tr><th>File Type</th><th>Count</th></tr>';
                    foreach ($results as $row) {
                        echo '<tr><td>' . htmlspecialchars($row['file_type']) . '</td><td>' . $row['count'] . '</td></tr>';
                    }
                    echo '</table>';
                }
                
                echo '<div class="status success">🎉 Database update completed successfully!</div>';
                
            } catch (PDOException $e) {
                echo '<div class="status error">❌ Database error: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
        } else {
            // Show current status without updating
            try {
                $pdo = new PDO("mysql:host=localhost;dbname=trust_scholarship_db", "root", "");
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                echo '<div class="status info">🔍 Database connection successful. Ready to check structure.</div>';
                
                // Check current structure
                $stmt = $pdo->query("SHOW COLUMNS FROM file_uploads WHERE Field = 'file_type'");
                $column = $stmt->fetch(PDO::FETCH_ASSOC);
                
                echo '<h3>📋 Current Database Status</h3>';
                echo '<div class="info"><strong>Current file_type ENUM:</strong><br>';
                echo '<div class="code">' . htmlspecialchars($column['Type']) . '</div></div>';
                
                // Check what file types are missing
                $currentEnum = $column['Type'];
                $requiredTypes = [
                    'photo', 'marksheets', 'fee_circular', 'signature', 'photo_id', 
                    'institute_id', 'address_proof', 'aadhaar_card', 'birth_certificate', 
                    'community_certificate', 'course_details', 'income_certificate', 
                    'attendance_certificate', 'fee_receipts', 'disability_certificate', 'other'
                ];
                
                $missingTypes = [];
                foreach ($requiredTypes as $type) {
                    if (strpos($currentEnum, "'$type'") === false) {
                        $missingTypes[] = $type;
                    }
                }
                
                if (empty($missingTypes)) {
                    echo '<div class="status success">✅ All required file types are already present! No update needed.</div>';
                } else {
                    echo '<div class="status warning">⚠️ Missing file types found: ' . implode(', ', $missingTypes) . '</div>';
                    echo '<div class="status info">Click "Update Database" to add missing file types.</div>';
                }
                
            } catch (PDOException $e) {
                echo '<div class="status error">❌ Cannot connect to database: ' . htmlspecialchars($e->getMessage()) . '</div>';
                echo '<div class="status info">Please ensure XAMPP MySQL is running and the database exists.</div>';
            }
        }
        ?>

        <form method="POST" style="margin-top: 20px;">
            <button type="submit" name="update_db" class="btn btn-success">🔄 Update Database</button>
            <button type="button" onclick="location.reload()" class="btn">🔍 Check Status</button>
        </form>

        <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
            <h4>📝 What this update does:</h4>
            <ul>
                <li>Adds missing file types to the file_uploads table ENUM</li>
                <li>Ensures all 16 document types from the application form are supported</li>
                <li>Maintains backward compatibility with existing data</li>
                <li>Aligns database structure with application form requirements</li>
            </ul>
        </div>
    </div>
</body>
</html>
