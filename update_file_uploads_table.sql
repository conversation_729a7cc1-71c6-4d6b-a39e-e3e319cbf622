-- Update file_uploads table to include all document types from application form
-- This script adds missing file types to the ENUM column

USE trust_scholarship_db;

-- First, let's check the current structure
DESCRIBE file_uploads;

-- Update the file_type ENUM to include all document types from the application form
ALTER TABLE file_uploads 
MODIFY COLUMN file_type ENUM(
    'photo',                    -- Passport size photograph (Step 1)
    'marksheets',              -- All marksheets (Step 3)
    'fee_circular',            -- Institute official fee circular (Step 4)
    'signature',               -- Digital signature (Step 5)
    'photo_id',                -- Photo ID (Aadhaar/Passport/Driving License)
    'institute_id',            -- Institute ID card
    'address_proof',           -- Address proof (Utility bill/Bank statement)
    'aadhaar_card',           -- A<PERSON><PERSON><PERSON> card copy
    'birth_certificate',       -- Birth certificate
    'community_certificate',   -- Community/Caste certificate
    'course_details',          -- Course fee details
    'income_certificate',      -- Family income certificate
    'attendance_certificate',  -- Attendance certificate (optional)
    'fee_receipts',            -- Fee receipts 2025-26 (optional)
    'disability_certificate',  -- Disability certificate (conditional)
    'other'                    -- Other documents
) NOT NULL;

-- Verify the updated structure
DESCRIBE file_uploads;

-- Show current file types in use
SELECT file_type, COUNT(*) as count 
FROM file_uploads 
GROUP BY file_type 
ORDER BY file_type;

COMMIT;
