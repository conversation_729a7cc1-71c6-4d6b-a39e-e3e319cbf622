<?php
// Database update script to modify file_uploads table ENUM
// This script updates the database structure as per analysis requirements

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'trust_scholarship_db';

try {
    // Create connection
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully!\n";
    
    // Check current file_uploads table structure
    echo "\n=== CURRENT FILE_UPLOADS TABLE STRUCTURE ===\n";
    $stmt = $pdo->query("DESCRIBE file_uploads");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        if ($row['Field'] == 'file_type') {
            echo "Current file_type ENUM: " . $row['Type'] . "\n";
        }
    }
    
    // Check if database needs updating
    $stmt = $pdo->query("SHOW COLUMNS FROM file_uploads WHERE Field = 'file_type'");
    $column = $stmt->fetch(PDO::FETCH_ASSOC);
    $currentEnum = $column['Type'];
    
    // Check if all required file types are present
    $requiredTypes = [
        'photo', 'marksheets', 'fee_circular', 'signature', 'photo_id', 
        'institute_id', 'address_proof', 'aadhaar_card', 'birth_certificate', 
        'community_certificate', 'course_details', 'income_certificate', 
        'attendance_certificate', 'fee_receipts', 'disability_certificate', 'other'
    ];
    
    $missingTypes = [];
    foreach ($requiredTypes as $type) {
        if (strpos($currentEnum, "'$type'") === false) {
            $missingTypes[] = $type;
        }
    }
    
    if (empty($missingTypes)) {
        echo "\n✅ All required file types are already present in the database!\n";
        echo "No update needed.\n";
    } else {
        echo "\n⚠️  Missing file types found: " . implode(', ', $missingTypes) . "\n";
        echo "Updating file_uploads table...\n";
        
        // Update the ENUM column
        $sql = "ALTER TABLE file_uploads 
                MODIFY COLUMN file_type ENUM(
                    'photo',
                    'marksheets',
                    'fee_circular',
                    'signature',
                    'photo_id',
                    'institute_id',
                    'address_proof',
                    'aadhaar_card',
                    'birth_certificate',
                    'community_certificate',
                    'course_details',
                    'income_certificate',
                    'attendance_certificate',
                    'fee_receipts',
                    'disability_certificate',
                    'other'
                ) NOT NULL";
        
        $pdo->exec($sql);
        echo "✅ File_uploads table updated successfully!\n";
    }
    
    // Verify the update
    echo "\n=== UPDATED FILE_UPLOADS TABLE STRUCTURE ===\n";
    $stmt = $pdo->query("DESCRIBE file_uploads");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        if ($row['Field'] == 'file_type') {
            echo "Updated file_type ENUM: " . $row['Type'] . "\n";
        }
    }
    
    // Check current file types in use
    echo "\n=== CURRENT FILE TYPES IN DATABASE ===\n";
    $stmt = $pdo->query("SELECT file_type, COUNT(*) as count FROM file_uploads GROUP BY file_type ORDER BY file_type");
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($results)) {
        echo "No files uploaded yet.\n";
    } else {
        foreach ($results as $row) {
            echo "- {$row['file_type']}: {$row['count']} files\n";
        }
    }
    
    // Check applications table structure
    echo "\n=== APPLICATIONS TABLE VERIFICATION ===\n";
    $stmt = $pdo->query("SELECT COUNT(*) as column_count FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = '$database' AND TABLE_NAME = 'applications'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Applications table has {$result['column_count']} columns\n";
    
    // Check if applications table exists and has required fields
    $stmt = $pdo->query("DESCRIBE applications");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $requiredFields = ['name', 'gender', 'date_of_birth', 'phone', 'email', 'status'];
    $missingFields = [];
    $existingFields = array_column($columns, 'Field');
    
    foreach ($requiredFields as $field) {
        if (!in_array($field, $existingFields)) {
            $missingFields[] = $field;
        }
    }
    
    if (empty($missingFields)) {
        echo "✅ All required fields are present in applications table\n";
    } else {
        echo "⚠️  Missing fields in applications table: " . implode(', ', $missingFields) . "\n";
    }
    
    echo "\n=== DATABASE UPDATE COMPLETE ===\n";
    echo "✅ Database structure is now aligned with application form requirements\n";
    
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
