// Using built-in fetch (Node.js 18+)

async function testAdminReview() {
    try {
        // First login as admin
        console.log('🔐 Logging in as admin...');
        const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'password123'
            })
        });

        const loginData = await loginResponse.json();
        if (!loginData.success) {
            console.error('❌ Login failed:', loginData.error);
            return;
        }

        const authToken = loginData.token;
        console.log('✅ Login successful');

        // Test approval with proper admin comments
        console.log('🔄 Testing application approval...');
        const approvalResponse = await fetch('http://localhost:3000/api/admin/applications/2/review', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`
            },
            body: JSON.stringify({
                status: 'approved',
                admin_comments: 'Application approved after thorough review. All documents are in order and the candidate meets all requirements.'
            })
        });

        const approvalData = await approvalResponse.json();
        console.log('📋 Approval response:', approvalData);

        if (approvalData.success) {
            console.log('✅ Application approved successfully!');
        } else {
            console.error('❌ Approval failed:', approvalData.error);
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testAdminReview();
