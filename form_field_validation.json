{"fields": [{"name": "user_id", "required": "backend-only"}, {"name": "application_id", "required": "backend-only"}, {"name": "name", "required": true}, {"name": "gender", "required": true}, {"name": "date_of_birth", "required": true}, {"name": "age", "required": true}, {"name": "place_of_birth", "required": true}, {"name": "marital_status", "required": true}, {"name": "religion", "required": true}, {"name": "category", "required": true}, {"name": "nationality", "required": true}, {"name": "present_address", "required": true}, {"name": "present_state", "required": true}, {"name": "present_country", "required": true}, {"name": "permanent_address", "required": "conditional"}, {"name": "permanent_state", "required": "conditional"}, {"name": "permanent_country", "required": "conditional"}, {"name": "phone", "required": true}, {"name": "whatsapp", "required": false}, {"name": "email", "required": true}, {"name": "family_total_income", "required": true}, {"name": "father_name", "required": false}, {"name": "father_age", "required": false}, {"name": "father_occupation", "required": false}, {"name": "father_income", "required": false}, {"name": "father_employment", "required": false}, {"name": "mother_name", "required": false}, {"name": "mother_age", "required": false}, {"name": "mother_occupation", "required": false}, {"name": "mother_income", "required": false}, {"name": "mother_employment", "required": false}, {"name": "spouse_name", "required": false}, {"name": "spouse_age", "required": false}, {"name": "spouse_occupation", "required": false}, {"name": "spouse_income", "required": false}, {"name": "spouse_employment", "required": false}, {"name": "sibling1_name", "required": false}, {"name": "sibling1_age", "required": false}, {"name": "sibling1_occupation", "required": false}, {"name": "sibling1_income", "required": false}, {"name": "sibling1_employment", "required": false}, {"name": "sibling2_name", "required": false}, {"name": "sibling2_age", "required": false}, {"name": "sibling2_occupation", "required": false}, {"name": "sibling2_income", "required": false}, {"name": "sibling2_employment", "required": false}, {"name": "sibling3_name", "required": false}, {"name": "sibling3_age", "required": false}, {"name": "sibling3_occupation", "required": false}, {"name": "sibling3_income", "required": false}, {"name": "sibling3_employment", "required": false}, {"name": "sibling4_name", "required": false}, {"name": "sibling4_age", "required": false}, {"name": "sibling4_occupation", "required": false}, {"name": "sibling4_income", "required": false}, {"name": "sibling4_employment", "required": false}, {"name": "is_disabled", "required": true}, {"name": "disability_type", "required": "conditional"}, {"name": "disability_percentage", "required": "conditional"}, {"name": "disability_description", "required": "conditional"}, {"name": "issuing_authority", "required": "conditional"}, {"name": "certificate_number", "required": "conditional"}, {"name": "issue_date", "required": "conditional"}, {"name": "sslc_institution", "required": false}, {"name": "sslc_type", "required": false}, {"name": "sslc_board", "required": false}, {"name": "sslc_marks", "required": false}, {"name": "sslc_year", "required": false}, {"name": "hsc_institution", "required": false}, {"name": "hsc_type", "required": false}, {"name": "hsc_board", "required": false}, {"name": "hsc_marks", "required": false}, {"name": "hsc_year", "required": false}, {"name": "ug_institution", "required": false}, {"name": "ug_type", "required": false}, {"name": "ug_board", "required": false}, {"name": "ug_marks", "required": false}, {"name": "ug_year", "required": false}, {"name": "vocational_institution", "required": false}, {"name": "vocational_type", "required": false}, {"name": "vocational_board", "required": false}, {"name": "vocational_marks", "required": false}, {"name": "vocational_year", "required": false}, {"name": "diploma_institution", "required": false}, {"name": "diploma_type", "required": false}, {"name": "diploma_board", "required": false}, {"name": "diploma_marks", "required": false}, {"name": "diploma_year", "required": false}, {"name": "others_institution", "required": false}, {"name": "others_type", "required": false}, {"name": "others_board", "required": false}, {"name": "others_marks", "required": false}, {"name": "others_year", "required": false}, {"name": "current_course", "required": true}, {"name": "course_duration", "required": true}, {"name": "course_year", "required": true}, {"name": "roll_number", "required": true}, {"name": "institution_name", "required": true}, {"name": "institution_type", "required": true}, {"name": "institution_address", "required": true}, {"name": "institution_phone", "required": true}, {"name": "institution_email", "required": true}, {"name": "institution_website", "required": false}, {"name": "term_fees", "required": true}, {"name": "tuition_fees", "required": true}, {"name": "other_fees", "required": true}, {"name": "scholarship_amount_figures", "required": true}, {"name": "scholarship_amount_words", "required": true}, {"name": "previous_awt_scholarship", "required": false}, {"name": "other_scholarships", "required": false}, {"name": "applied_scholarships", "required": false}, {"name": "reference1_name", "required": true}, {"name": "reference1_phone", "required": true}, {"name": "reference1_email", "required": true}, {"name": "reference1_position", "required": true}, {"name": "reference2_name", "required": true}, {"name": "reference2_phone", "required": true}, {"name": "reference2_email", "required": true}, {"name": "reference2_position", "required": true}, {"name": "extracurricular", "required": false}, {"name": "other_info", "required": false}, {"name": "goals", "required": true}, {"name": "declaration_place", "required": true}, {"name": "declaration_date", "required": true}, {"name": "status", "required": "backend-only"}]}