// Debug script to count columns and parameters in the INSERT statement

const insertColumns = `user_id, application_id, name, gender, date_of_birth, age, place_of_birth,
marital_status, religion, category, nationality, present_address, present_state,
present_country, permanent_address, permanent_state, permanent_country,
phone, whatsapp, email, family_total_income,
father_name, father_age, father_occupation, father_income, father_employment,
mother_name, mother_age, mother_occupation, mother_income, mother_employment,
spouse_name, spouse_age, spouse_occupation, spouse_income, spouse_employment,
sibling1_name, sibling1_age, sibling1_occupation, sibling1_income, sibling1_employment,
sibling2_name, sibling2_age, sibling2_occupation, sibling2_income, sibling2_employment,
sibling3_name, sibling3_age, sibling3_occupation, sibling3_income, sibling3_employment,
sibling4_name, sibling4_age, sibling4_occupation, sibling4_income, sibling4_employment,
is_disabled, disability_type, disability_percentage, disability_description,
issuing_authority, certificate_number, issue_date,
sslc_institution, sslc_type, sslc_board, sslc_marks, sslc_year,
hsc_institution, hsc_type, hsc_board, hsc_marks, hsc_year,
ug_institution, ug_type, ug_board, ug_marks, ug_year,
vocational_institution, vocational_type, vocational_board, vocational_marks, vocational_year,
diploma_institution, diploma_type, diploma_board, diploma_marks, diploma_year,
others_institution, others_type, others_board, others_marks, others_year,
current_course, course_duration, course_year, roll_number,
institution_name, institution_type, institution_address, institution_phone,
institution_email, institution_website, term_fees, tuition_fees, other_fees,
scholarship_amount_figures, scholarship_amount_words,
previous_awt_scholarship, other_scholarships, applied_scholarships,
reference1_name, reference1_phone, reference1_email, reference1_position,
reference2_name, reference2_phone, reference2_email, reference2_position,
extracurricular, other_info, goals,
declaration_place, declaration_date, status`;

// Count columns
const columns = insertColumns.split(',').map(col => col.trim()).filter(col => col.length > 0);
console.log('📊 Column Analysis:');
console.log(`Total columns in INSERT: ${columns.length}`);
console.log('\nColumns list:');
columns.forEach((col, index) => {
    console.log(`${index + 1}. ${col}`);
});

// Count parameters in the actual code
const parameterLines = [
    'userId, applicationId, applicationData.name || \'\', applicationData.gender || \'\'',
    'applicationData.date_of_birth || null, applicationData.age || null, applicationData.place_of_birth || \'\'',
    'applicationData.marital_status || \'\', applicationData.religion || \'\', applicationData.category || \'\'',
    'applicationData.nationality || \'\', applicationData.present_address || \'\', applicationData.present_state || \'\'',
    'applicationData.present_country || \'\', applicationData.permanent_address || \'\', applicationData.permanent_state || \'\'',
    'applicationData.permanent_country || \'\', applicationData.phone || \'\', applicationData.whatsapp || \'\'',
    'applicationData.email || \'\', applicationData.family_total_income || 0',
    'applicationData.father_name || \'\', applicationData.father_age || null, applicationData.father_occupation || \'\'',
    'applicationData.father_income || 0, applicationData.father_employment || \'\'',
    'applicationData.mother_name || \'\', applicationData.mother_age || null, applicationData.mother_occupation || \'\'',
    'applicationData.mother_income || 0, applicationData.mother_employment || \'\'',
    'applicationData.spouse_name || \'\', applicationData.spouse_age || null, applicationData.spouse_occupation || \'\'',
    'applicationData.spouse_income || 0, applicationData.spouse_employment || \'\'',
    'applicationData.sibling1_name || \'\', applicationData.sibling1_age || null, applicationData.sibling1_occupation || \'\'',
    'applicationData.sibling1_income || 0, applicationData.sibling1_employment || \'\'',
    'applicationData.sibling2_name || \'\', applicationData.sibling2_age || null, applicationData.sibling2_occupation || \'\'',
    'applicationData.sibling2_income || 0, applicationData.sibling2_employment || \'\'',
    'applicationData.sibling3_name || \'\', applicationData.sibling3_age || null, applicationData.sibling3_occupation || \'\'',
    'applicationData.sibling3_income || 0, applicationData.sibling3_employment || \'\'',
    'applicationData.sibling4_name || \'\', applicationData.sibling4_age || null, applicationData.sibling4_occupation || \'\'',
    'applicationData.sibling4_income || 0, applicationData.sibling4_employment || \'\'',
    'applicationData.is_disabled === true || applicationData.is_disabled === \'Yes\' ? 1 : 0, applicationData.disability_type || \'\'',
    'applicationData.disability_percentage || null, applicationData.disability_description || \'\'',
    'applicationData.issuing_authority || \'\', applicationData.certificate_number || \'\', applicationData.issue_date || \'\'',
    'applicationData.sslc_institution || \'\', applicationData.sslc_type || \'\', applicationData.sslc_board || \'\'',
    'applicationData.sslc_marks || \'\', applicationData.sslc_year || null',
    'applicationData.hsc_institution || \'\', applicationData.hsc_type || \'\', applicationData.hsc_board || \'\'',
    'applicationData.hsc_marks || \'\', applicationData.hsc_year || null',
    'applicationData.ug_institution || \'\', applicationData.ug_type || \'\', applicationData.ug_board || \'\'',
    'applicationData.ug_marks || \'\', applicationData.ug_year || null',
    'applicationData.vocational_institution || \'\', applicationData.vocational_type || \'\', applicationData.vocational_board || \'\'',
    'applicationData.vocational_marks || \'\', applicationData.vocational_year || null',
    'applicationData.diploma_institution || \'\', applicationData.diploma_type || \'\', applicationData.diploma_board || \'\'',
    'applicationData.diploma_marks || \'\', applicationData.diploma_year || null',
    'applicationData.others_institution || \'\', applicationData.others_type || \'\', applicationData.others_board || \'\'',
    'applicationData.others_marks || \'\', applicationData.others_year || null',
    'applicationData.current_course || \'\', applicationData.course_duration || \'\', applicationData.course_year || \'\'',
    'applicationData.roll_number || \'\', applicationData.institution_name || \'\', applicationData.institution_type || \'\'',
    'applicationData.institution_address || \'\', applicationData.institution_phone || \'\', applicationData.institution_email || \'\'',
    'applicationData.institution_website || \'\', applicationData.term_fees || \'\', applicationData.tuition_fees || 0',
    'applicationData.other_fees || 0, applicationData.scholarship_amount_figures || 0, applicationData.scholarship_amount_words || \'\'',
    'applicationData.previous_awt_scholarship || \'\', applicationData.other_scholarships || \'\'',
    'applicationData.applied_scholarships || \'\', applicationData.reference1_name || \'\', applicationData.reference1_phone || \'\'',
    'applicationData.reference1_email || \'\', applicationData.reference1_position || \'\', applicationData.reference2_name || \'\'',
    'applicationData.reference2_phone || \'\', applicationData.reference2_email || \'\', applicationData.reference2_position || \'\'',
    'applicationData.extracurricular || \'\', applicationData.other_info || \'\', applicationData.goals || \'\'',
    'applicationData.declaration_place || \'\', applicationData.declaration_date || null, \'draft\''
];

// Count parameters
let totalParams = 0;
console.log('\n📊 Parameter Analysis:');
parameterLines.forEach((line, index) => {
    const params = line.split(',').map(p => p.trim()).filter(p => p.length > 0);
    totalParams += params.length;
    console.log(`Line ${index + 1}: ${params.length} parameters`);
});

console.log(`\nTotal parameters: ${totalParams}`);
console.log(`Expected placeholders: 101`);
console.log(`Column count: ${columns.length}`);
console.log(`Parameter count: ${totalParams}`);

if (columns.length !== totalParams) {
    console.log(`\n❌ MISMATCH: ${columns.length} columns vs ${totalParams} parameters`);
    console.log(`Difference: ${columns.length - totalParams}`);
} else {
    console.log('\n✅ Column and parameter counts match!');
}

if (columns.length !== 101) {
    console.log(`\n❌ MISMATCH: Expected 101 placeholders but have ${columns.length} columns`);
    console.log(`Need to change Array(101) to Array(${columns.length})`);
} else {
    console.log('\n✅ Placeholder count matches column count!');
}
