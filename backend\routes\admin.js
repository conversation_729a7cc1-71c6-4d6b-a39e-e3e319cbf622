const express = require('express');
const { executeQuery } = require('../config/database');
const { verifyToken, requireAdmin, logActivity } = require('../middleware/auth');
const { validateApplicationReview, validateSystemSettings, validateApplicationEdit, validateId, validatePagination } = require('../middleware/validation');
const { sendEmail } = require('../utils/email');

const router = express.Router();

// @route   GET /api/admin/dashboard
// @desc    Get admin dashboard statistics
// @access  Private (Admin)
router.get('/dashboard', verifyToken, requireAdmin, async (req, res) => {
    try {
        // Get statistics
        const stats = await executeQuery(`
            SELECT 
                (SELECT COUNT(*) FROM users WHERE role = 'candidate') as total_candidates,
                (SELECT COUNT(*) FROM applications) as total_applications,
                (SELECT COUNT(*) FROM applications WHERE status = 'draft') as draft_applications,
                (SELECT COUNT(*) FROM applications WHERE status = 'submitted') as submitted_applications,
                (SELECT COUNT(*) FROM applications WHERE status = 'under_review') as under_review_applications,
                (SELECT COUNT(*) FROM applications WHERE status = 'approved') as approved_applications,
                (SELECT COUNT(*) FROM applications WHERE status = 'rejected') as rejected_applications,
                (SELECT COUNT(*) FROM applications WHERE submitted_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)) as applications_last_30_days,
                (SELECT COUNT(*) FROM applications WHERE submitted_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as applications_last_7_days,
                (SELECT SUM(scholarship_amount_figures) FROM applications WHERE status = 'approved') as total_approved_amount
        `);
        
        // Get recent applications
        const recentApplications = await executeQuery(`
            SELECT a.id, a.application_id, a.name, a.current_course, a.institution_name,
                   a.scholarship_amount_figures, a.status, a.submitted_at, a.created_at,
                   u.email as user_email
            FROM applications a
            JOIN users u ON a.user_id = u.id
            WHERE a.status IN ('submitted', 'under_review')
            ORDER BY a.submitted_at DESC
            LIMIT 10
        `);
        
        // Get monthly application trends (last 12 months)
        const monthlyTrends = await executeQuery(`
            SELECT 
                DATE_FORMAT(submitted_at, '%Y-%m') as month,
                COUNT(*) as count,
                SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_count,
                SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count
            FROM applications 
            WHERE submitted_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            GROUP BY DATE_FORMAT(submitted_at, '%Y-%m')
            ORDER BY month DESC
        `);
        
        res.json({
            success: true,
            data: {
                statistics: stats[0],
                recent_applications: recentApplications,
                monthly_trends: monthlyTrends
            }
        });
        
    } catch (error) {
        console.error('Admin dashboard error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch dashboard data'
        });
    }
});

// @route   GET /api/admin/applications
// @desc    Get all applications with filters
// @access  Private (Admin)
router.get('/applications', verifyToken, requireAdmin, validatePagination, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const offset = (page - 1) * limit;
        const status = req.query.status;
        const search = req.query.search;
        const sortBy = req.query.sortBy || 'submitted_at';
        const sortOrder = req.query.sortOrder || 'DESC';
        
        // Build WHERE clause
        let whereClause = '1=1';
        let queryParams = [];
        
        if (status && status !== 'all') {
            whereClause += ' AND a.status = ?';
            queryParams.push(status);
        }
        
        if (search) {
            whereClause += ' AND (a.name LIKE ? OR a.application_id LIKE ? OR a.email LIKE ? OR a.current_course LIKE ?)';
            const searchTerm = `%${search}%`;
            queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm);
        }
        
        // Validate sort column
        const allowedSortColumns = ['name', 'submitted_at', 'status', 'scholarship_amount_figures', 'created_at'];
        const sortColumn = allowedSortColumns.includes(sortBy) ? sortBy : 'submitted_at';
        const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
        
        // Get applications
        const applications = await executeQuery(`
            SELECT a.id, a.application_id, a.name, a.gender, a.age, a.current_course,
                   a.institution_name, a.scholarship_amount_figures, a.status, a.submitted_at,
                   a.created_at, a.updated_at, u.email as user_email, u.phone as user_phone,
                   admin.name as reviewed_by_name, a.reviewed_at, a.admin_comments, a.editable
            FROM applications a
            JOIN users u ON a.user_id = u.id
            LEFT JOIN users admin ON a.reviewed_by = admin.id
            WHERE ${whereClause}
            ORDER BY a.${sortColumn} ${order}
            LIMIT ? OFFSET ?
        `, [...queryParams, limit, offset]);

        // Get file uploads for each application
        for (let app of applications) {
            const files = await executeQuery(
                'SELECT * FROM file_uploads WHERE application_id = ? ORDER BY file_type',
                [app.id]
            );
            app.file_uploads = files;
        }
        
        // Get total count
        const countResult = await executeQuery(`
            SELECT COUNT(*) as total
            FROM applications a
            JOIN users u ON a.user_id = u.id
            WHERE ${whereClause}
        `, queryParams);
        
        const total = countResult[0].total;
        const totalPages = Math.ceil(total / limit);
        
        res.json({
            success: true,
            data: {
                applications,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages,
                    hasNext: page < totalPages,
                    hasPrev: page > 1
                },
                filters: {
                    status,
                    search,
                    sortBy: sortColumn,
                    sortOrder: order
                }
            }
        });
        
    } catch (error) {
        console.error('Get admin applications error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch applications'
        });
    }
});

// @route   GET /api/admin/applications/:id
// @desc    Get specific application details
// @access  Private (Admin)
router.get('/applications/:id', verifyToken, requireAdmin, validateId, async (req, res) => {
    try {
        // Get application with user details - ALL FIELDS
        const applications = await executeQuery(`
            SELECT a.*, u.email as user_email, u.phone as user_phone, u.created_at as user_registered_at,
                   admin.name as reviewed_by_name, a.reviewed_at
            FROM applications a
            JOIN users u ON a.user_id = u.id
            LEFT JOIN users admin ON a.reviewed_by = admin.id
            WHERE a.id = ?
        `, [req.params.id]);
        
        if (applications.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Application not found'
            });
        }
        
        const application = applications[0];
        
        // Get family members
        const familyMembers = await executeQuery(
            'SELECT * FROM family_members WHERE application_id = ? ORDER BY relationship',
            [application.id]
        );
        
        // Get disability details
        const disabilityDetails = await executeQuery(
            'SELECT * FROM disability_details WHERE application_id = ?',
            [application.id]
        );
        
        // Get educational qualifications
        const educationalQualifications = await executeQuery(
            'SELECT * FROM educational_qualifications WHERE application_id = ? ORDER BY qualification_type',
            [application.id]
        );
        
        // Get file uploads
        const fileUploads = await executeQuery(
            'SELECT * FROM file_uploads WHERE application_id = ? ORDER BY file_type',
            [application.id]
        );
        
        res.json({
            success: true,
            data: {
                application: {
                    ...application,
                    family_members: familyMembers,
                    disability_details: disabilityDetails[0] || null,
                    educational_qualifications: educationalQualifications,
                    file_uploads: fileUploads
                }
            }
        });
        
    } catch (error) {
        console.error('Get admin application error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch application details'
        });
    }
});

// @route   PUT /api/admin/applications/:id/review
// @desc    Review application (approve/reject)
// @access  Private (Admin)
router.put('/applications/:id/review', verifyToken, requireAdmin, validateId, validateApplicationReview, logActivity('review_application'), async (req, res) => {
    try {
        const { status, admin_comments } = req.body;
        const applicationId = req.params.id;

        // Get application details
        const applications = await executeQuery(
            'SELECT id, name, email, application_id, status as current_status, user_id FROM applications WHERE id = ?',
            [applicationId]
        );

        if (applications.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Application not found'
            });
        }

        const application = applications[0];

        // Check if application can be reviewed
        if (application.current_status === 'draft') {
            return res.status(400).json({
                success: false,
                error: 'Cannot review draft application'
            });
        }

        // Set editable flag based on status
        const editable = status === 'rejected';

        // Update application status
        await executeQuery(
            `UPDATE applications SET
                status = ?, admin_comments = ?, reviewed_by = ?, reviewed_at = NOW(),
                editable = ?, updated_at = NOW()
             WHERE id = ?`,
            [status, admin_comments, req.user.id, editable, applicationId]
        );

        // Record in application history
        await executeQuery(
            `INSERT INTO application_history (application_id, previous_status, new_status, admin_id, admin_comments, action_type)
             VALUES (?, ?, ?, ?, ?, 'status_change')`,
            [applicationId, application.current_status, status, req.user.id, admin_comments]
        );
        
        // Send notification email to applicant
        try {
            const emailTemplate = status === 'approved' ? 'application-approved' : 'application-rejected';
            const emailSubject = status === 'approved'
                ? 'Application Approved - Access Welfare Trust'
                : 'Application Update - Access Welfare Trust';

            await sendEmail({
                to: application.email,
                subject: emailSubject,
                template: emailTemplate,
                data: {
                    name: application.name,
                    applicationId: application.application_id,
                    status: status,
                    adminComments: admin_comments,
                    reviewedBy: req.user.name,
                    canEdit: editable
                }
            });
        } catch (emailError) {
            console.error('Failed to send notification email:', emailError);
            // Don't fail the review if email fails
        }

        // Create notification for user
        await executeQuery(
            `INSERT INTO notifications (user_id, title, message, type)
             VALUES (?, ?, ?, ?)`,
            [
                application.user_id,
                `Application ${status.charAt(0).toUpperCase() + status.slice(1)}`,
                `Your scholarship application has been ${status}. ${admin_comments ? 'Admin Comments: ' + admin_comments : ''}${editable ? ' You can now edit and resubmit your application.' : ''}`,
                status === 'approved' ? 'success' : 'warning'
            ]
        );
        
        res.json({
            success: true,
            message: `Application ${status} successfully`
        });
        
    } catch (error) {
        console.error('Review application error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to review application'
        });
    }
});

// @route   GET /api/admin/users
// @desc    Get all users
// @access  Private (Admin)
router.get('/users', verifyToken, requireAdmin, validatePagination, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const offset = (page - 1) * limit;
        const role = req.query.role;
        const search = req.query.search;
        
        // Build WHERE clause
        let whereClause = '1=1';
        let queryParams = [];
        
        if (role && role !== 'all') {
            whereClause += ' AND role = ?';
            queryParams.push(role);
        }
        
        if (search) {
            whereClause += ' AND (name LIKE ? OR email LIKE ?)';
            const searchTerm = `%${search}%`;
            queryParams.push(searchTerm, searchTerm);
        }
        
        // Get users
        const users = await executeQuery(`
            SELECT id, name, email, phone, role, email_verified, is_active, 
                   last_login, created_at,
                   (SELECT COUNT(*) FROM applications WHERE user_id = users.id) as application_count
            FROM users
            WHERE ${whereClause}
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        `, [...queryParams, limit, offset]);
        
        // Get total count
        const countResult = await executeQuery(`
            SELECT COUNT(*) as total FROM users WHERE ${whereClause}
        `, queryParams);
        
        const total = countResult[0].total;
        const totalPages = Math.ceil(total / limit);
        
        res.json({
            success: true,
            data: {
                users,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages,
                    hasNext: page < totalPages,
                    hasPrev: page > 1
                }
            }
        });
        
    } catch (error) {
        console.error('Get admin users error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch users'
        });
    }
});

// @route   PUT /api/admin/users/:id/status
// @desc    Update user status (activate/deactivate)
// @access  Private (Admin)
router.put('/users/:id/status', verifyToken, requireAdmin, validateId, logActivity('update_user_status'), async (req, res) => {
    try {
        const { is_active } = req.body;
        const userId = req.params.id;
        
        // Prevent admin from deactivating themselves
        if (parseInt(userId) === req.user.id) {
            return res.status(400).json({
                success: false,
                error: 'Cannot change your own account status'
            });
        }
        
        // Update user status
        await executeQuery(
            'UPDATE users SET is_active = ?, updated_at = NOW() WHERE id = ?',
            [is_active, userId]
        );
        
        res.json({
            success: true,
            message: `User ${is_active ? 'activated' : 'deactivated'} successfully`
        });
        
    } catch (error) {
        console.error('Update user status error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update user status'
        });
    }
});

// @route   GET /api/admin/test-files
// @desc    Test file uploads fetching
// @access  Public (for testing)
router.get('/test-files', async (req, res) => {
    try {
        // Get all applications
        const applications = await executeQuery(`
            SELECT id, name FROM applications LIMIT 3
        `);

        console.log('📋 Found applications:', applications);

        // Get file uploads for each application
        for (let app of applications) {
            const files = await executeQuery(
                'SELECT * FROM file_uploads WHERE application_id = ? ORDER BY file_type',
                [app.id]
            );
            console.log(`📁 Application ${app.id} (${app.name}) has ${files.length} files:`, files.map(f => f.original_name));
            app.file_uploads = files;
        }

        res.json({
            success: true,
            data: applications
        });

    } catch (error) {
        console.error('Test files error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to test files'
        });
    }
});

// @route   GET /api/admin/settings
// @desc    Get system settings
// @access  Private (Admin)
router.get('/settings', verifyToken, requireAdmin, async (req, res) => {
    try {
        const settings = await executeQuery('SELECT * FROM system_settings ORDER BY setting_key');

        // Convert to key-value object for easier frontend consumption
        const settingsObj = {};
        settings.forEach(setting => {
            let value = setting.setting_value;

            // Convert based on type
            if (setting.setting_type === 'boolean') {
                value = value === 'true';
            } else if (setting.setting_type === 'number') {
                value = parseFloat(value);
            } else if (setting.setting_type === 'json') {
                try {
                    value = JSON.parse(value);
                } catch (e) {
                    console.error('Failed to parse JSON setting:', setting.setting_key);
                }
            }

            settingsObj[setting.setting_key] = {
                value: value,
                type: setting.setting_type,
                description: setting.description,
                updated_at: setting.updated_at
            };
        });

        res.json({
            success: true,
            data: settingsObj
        });

    } catch (error) {
        console.error('Get settings error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch settings'
        });
    }
});

// @route   PUT /api/admin/settings/:key
// @desc    Update system setting
// @access  Private (Admin)
router.put('/settings/:key', verifyToken, requireAdmin, validateSystemSettings, logActivity('update_setting'), async (req, res) => {
    try {
        const { setting_value, setting_type = 'string' } = req.body;
        const settingKey = req.params.key;

        // Validate setting value based on type
        let processedValue = setting_value;
        if (setting_type === 'boolean') {
            processedValue = setting_value ? 'true' : 'false';
        } else if (setting_type === 'number') {
            processedValue = parseFloat(setting_value).toString();
        } else if (setting_type === 'json') {
            try {
                JSON.parse(setting_value); // Validate JSON
                processedValue = setting_value;
            } catch (e) {
                return res.status(400).json({
                    success: false,
                    error: 'Invalid JSON format'
                });
            }
        }

        // Update or insert setting
        await executeQuery(
            `INSERT INTO system_settings (setting_key, setting_value, setting_type)
             VALUES (?, ?, ?)
             ON DUPLICATE KEY UPDATE
             setting_value = VALUES(setting_value),
             setting_type = VALUES(setting_type),
             updated_at = CURRENT_TIMESTAMP`,
            [settingKey, processedValue, setting_type]
        );

        res.json({
            success: true,
            message: 'Setting updated successfully'
        });

    } catch (error) {
        console.error('Update setting error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update setting'
        });
    }
});

// @route   GET /api/admin/application-history/:id
// @desc    Get application history
// @access  Private (Admin)
router.get('/application-history/:id', verifyToken, requireAdmin, validateId, async (req, res) => {
    try {
        const applicationId = req.params.id;

        const history = await executeQuery(`
            SELECT ah.*, u.name as admin_name, u.email as admin_email
            FROM application_history ah
            LEFT JOIN users u ON ah.admin_id = u.id
            WHERE ah.application_id = ?
            ORDER BY ah.created_at DESC
        `, [applicationId]);

        res.json({
            success: true,
            data: history
        });

    } catch (error) {
        console.error('Get application history error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch application history'
        });
    }
});

module.exports = router;
