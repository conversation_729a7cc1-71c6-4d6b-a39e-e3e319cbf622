<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Application Creation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #007bff; color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .code { background: #f8f9fa; padding: 10px; border-left: 4px solid #007bff; margin: 10px 0; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test Application Creation</h1>
            <p>Test the fixed backend application creation endpoint</p>
        </div>

        <div class="form-group">
            <label for="authToken">Authentication Token:</label>
            <input type="text" id="authToken" placeholder="Enter your JWT token here">
            <small>Get this from browser developer tools after logging in</small>
        </div>

        <button class="btn" onclick="testApplicationCreation()">🚀 Test Application Creation</button>
        <button class="btn" onclick="clearResults()">🧹 Clear Results</button>

        <div id="results"></div>
    </div>

    <script>
        async function testApplicationCreation() {
            const authToken = document.getElementById('authToken').value.trim();
            const resultsDiv = document.getElementById('results');
            
            if (!authToken) {
                resultsDiv.innerHTML = '<div class="status error">❌ Please enter an authentication token</div>';
                return;
            }

            resultsDiv.innerHTML = '<div class="status info">🔄 Testing application creation...</div>';

            // Sample application data
            const applicationData = {
                // Personal Details
                name: 'Test User',
                gender: 'Male',
                date_of_birth: '2000-01-01',
                age: 25,
                place_of_birth: 'Test City',
                marital_status: 'Single',
                religion: 'Hindu',
                category: 'General',
                nationality: 'Indian',
                
                // Address Details
                present_address: 'Test Address',
                present_state: 'Test State',
                present_country: 'India',
                permanent_address: 'Test Address',
                permanent_state: 'Test State',
                permanent_country: 'India',
                
                // Contact Details
                phone: '+************',
                whatsapp: '+************',
                email: '<EMAIL>',
                family_total_income: 50000,

                // Disability Details
                is_disabled: 'false',

                // Family Details
                father_name: 'Test Father',
                father_age: 50,
                father_occupation: 'Business',
                father_income: 30000,
                father_employment: 'Self Employed',
                
                mother_name: 'Test Mother',
                mother_age: 45,
                mother_occupation: 'Housewife',
                mother_income: 0,
                mother_employment: 'Homemaker',
                
                // Educational Details
                current_course: 'B.Tech Computer Science',
                course_duration: '4 years',
                course_year: '2nd Year',
                roll_number: 'CS2023001',
                
                // Institution Details
                institution_name: 'Test Engineering College',
                institution_type: 'Private',
                institution_address: 'Test College Address, Test City, Test State - 123456',
                institution_phone: '+************',
                institution_email: '<EMAIL>',
                institution_website: 'www.testcollege.edu',
                
                // Scholarship Details
                scholarship_amount_figures: 25000,
                scholarship_amount_words: 'Twenty Five Thousand Only',
                
                // Declaration
                declaration_place: 'Test City',
                declaration_date: '2025-07-01'
            };

            try {
                const response = await fetch('http://localhost:3000/api/applications', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(applicationData)
                });

                const result = await response.json();

                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="status success">✅ Application created successfully!</div>
                        <div class="code">Response:
${JSON.stringify(result, null, 2)}</div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="status error">❌ Application creation failed</div>
                        <div class="code">Error Response:
${JSON.stringify(result, null, 2)}</div>
                        <div class="code">Status: ${response.status} ${response.statusText}</div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="status error">❌ Network error occurred</div>
                    <div class="code">Error: ${error.message}</div>
                `;
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Auto-fill token from localStorage if available
        window.onload = function() {
            const savedToken = localStorage.getItem('authToken');
            if (savedToken) {
                document.getElementById('authToken').value = savedToken;
            }
        };

        // Save token to localStorage when changed
        document.getElementById('authToken').addEventListener('change', function() {
            localStorage.setItem('authToken', this.value);
        });
    </script>
</body>
</html>
