CREATE DATABASE IF NOT EXISTS trust_scholarship_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE trust_scholarship_db;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password VARCHAR(255) NOT NULL,
    role ENUM('candidate', 'admin') DEFAULT 'candidate',
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    reset_token VARCHAR(255),
    reset_token_expires DATETIME,
    last_login DATETIME,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_created_at (created_at)
);

-- Applications table with all required fields
CREATE TABLE IF NOT EXISTS applications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    application_id VARCHAR(20) UNIQUE NOT NULL,
    
    -- Personal details
    name VARCHAR(255),
    gender VARCHAR(10),
    date_of_birth DATE,
    age INT,
    place_of_birth VARCHAR(255),
    marital_status VARCHAR(20),
    religion VARCHAR(100),
    category VARCHAR(50),
    nationality VARCHAR(100),
    
    -- Address details
    present_address TEXT,
    present_state VARCHAR(100),
    present_country VARCHAR(100),
    permanent_address TEXT,
    permanent_state VARCHAR(100),
    permanent_country VARCHAR(100),
    
    -- Contact details
    phone VARCHAR(20),
    whatsapp VARCHAR(20),
    email VARCHAR(255),
    family_total_income DECIMAL(12,2),
    
    -- Family details
    father_name VARCHAR(255),
    father_age INT,
    father_occupation VARCHAR(255),
    father_income DECIMAL(12,2),
    father_employment VARCHAR(500),
    
    mother_name VARCHAR(255),
    mother_age INT,
    mother_occupation VARCHAR(255),
    mother_income DECIMAL(12,2),
    mother_employment VARCHAR(500),
    
    spouse_name VARCHAR(255),
    spouse_age INT,
    spouse_occupation VARCHAR(255),
    spouse_income DECIMAL(12,2),
    spouse_employment VARCHAR(500),
    
    sibling1_name VARCHAR(255),
    sibling1_age INT,
    sibling1_occupation VARCHAR(255),
    sibling1_income DECIMAL(12,2),
    sibling1_employment VARCHAR(500),
    
    sibling2_name VARCHAR(255),
    sibling2_age INT,
    sibling2_occupation VARCHAR(255),
    sibling2_income DECIMAL(12,2),
    sibling2_employment VARCHAR(500),
    
    sibling3_name VARCHAR(255),
    sibling3_age INT,
    sibling3_occupation VARCHAR(255),
    sibling3_income DECIMAL(12,2),
    sibling3_employment VARCHAR(500),
    
    sibling4_name VARCHAR(255),
    sibling4_age INT,
    sibling4_occupation VARCHAR(255),
    sibling4_income DECIMAL(12,2),
    sibling4_employment VARCHAR(500),
    
    -- Disability details
    is_disabled BOOLEAN DEFAULT FALSE,
    disability_type VARCHAR(255),
    disability_percentage INT,
    disability_description TEXT,
    issuing_authority VARCHAR(255),
    certificate_number VARCHAR(255),
    issue_date DATE,
    
    -- Educational qualifications
    sslc_institution VARCHAR(255),
    sslc_type VARCHAR(100),
    sslc_board VARCHAR(255),
    sslc_marks VARCHAR(50),
    sslc_year INT,
    
    hsc_institution VARCHAR(255),
    hsc_type VARCHAR(100),
    hsc_board VARCHAR(255),
    hsc_marks VARCHAR(50),
    hsc_year INT,
    
    ug_institution VARCHAR(255),
    ug_type VARCHAR(100),
    ug_board VARCHAR(255),
    ug_marks VARCHAR(50),
    ug_year INT,
    
    vocational_institution VARCHAR(255),
    vocational_type VARCHAR(100),
    vocational_board VARCHAR(255),
    vocational_marks VARCHAR(50),
    vocational_year INT,
    
    diploma_institution VARCHAR(255),
    diploma_type VARCHAR(100),
    diploma_board VARCHAR(255),
    diploma_marks VARCHAR(50),
    diploma_year INT,
    
    others_institution VARCHAR(255),
    others_type VARCHAR(100),
    others_board VARCHAR(255),
    others_marks VARCHAR(50),
    others_year INT,
    
    -- Current course details
    current_course VARCHAR(255),
    course_duration VARCHAR(100),
    course_year VARCHAR(50),
    roll_number VARCHAR(100),
    
    -- Institution details
    institution_name VARCHAR(255),
    institution_type VARCHAR(100),
    institution_address TEXT,
    institution_phone VARCHAR(20),
    institution_email VARCHAR(255),
    institution_website VARCHAR(255),
    
    -- Fee details
    term_fees VARCHAR(100),
    tuition_fees DECIMAL(12,2),
    other_fees DECIMAL(12,2),
    scholarship_amount_figures DECIMAL(12,2),
    scholarship_amount_words VARCHAR(255),
    
    -- Scholarship details
    previous_awt_scholarship VARCHAR(500),
    other_scholarships VARCHAR(500),
    applied_scholarships VARCHAR(500),
    
    -- References
    reference1_name VARCHAR(255),
    reference1_phone VARCHAR(20),
    reference1_email VARCHAR(255),
    reference1_position VARCHAR(255),
    
    reference2_name VARCHAR(255),
    reference2_phone VARCHAR(20),
    reference2_email VARCHAR(255),
    reference2_position VARCHAR(255),
    
    -- Additional information
    extracurricular TEXT,
    other_info TEXT,
    goals TEXT,
    
    -- Declaration
    declaration_place VARCHAR(255),
    declaration_date DATE,
    
    -- Status and metadata
    status ENUM('draft', 'submitted', 'under_review', 'approved', 'rejected') DEFAULT 'draft',
    admin_notes TEXT,
    reviewed_by INT,
    reviewed_at DATETIME,
    submitted_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_application_id (application_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Family members table (keeping for backward compatibility)
CREATE TABLE IF NOT EXISTS family_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    relationship ENUM('Father', 'Mother', 'Spouse', 'Sibling') NOT NULL,
    name VARCHAR(255),
    age INT,
    occupation VARCHAR(255),
    annual_income DECIMAL(10,2),
    employment_details VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    INDEX idx_application_id (application_id)
);

-- Disability details table (keeping for backward compatibility)
CREATE TABLE IF NOT EXISTS disability_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    disability_type VARCHAR(255) NOT NULL,
    disability_percentage INT NOT NULL,
    disability_description TEXT,
    issuing_authority VARCHAR(255),
    certificate_number VARCHAR(255),
    issue_date_place VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    INDEX idx_application_id (application_id)
);

-- Educational qualifications table (keeping for backward compatibility)
CREATE TABLE IF NOT EXISTS educational_qualifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    qualification_level VARCHAR(255) NOT NULL,
    institution_name VARCHAR(255) NOT NULL,
    board_university VARCHAR(255),
    year_of_passing INT,
    percentage_cgpa DECIMAL(5,2),
    subjects VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    INDEX idx_application_id (application_id)
);

-- File uploads table
CREATE TABLE IF NOT EXISTS file_uploads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    application_id INT NOT NULL,
    file_type ENUM('photo', 'signature', 'photo_id', 'institute_id', 'address_proof',
                  'income_certificate', 'disability_certificate', 'marksheets', 'fee_circular',
                  'aadhaar_card', 'birth_certificate', 'community_certificate', 'course_details',
                  'attendance_certificate', 'fee_receipts', 'other') NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    INDEX idx_application_id (application_id),
    INDEX idx_file_type (file_type)
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    notification_type VARCHAR(50),
    related_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at)
);

-- Audit log table
CREATE TABLE IF NOT EXISTS audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_table_name (table_name),
    INDEX idx_created_at (created_at)
);