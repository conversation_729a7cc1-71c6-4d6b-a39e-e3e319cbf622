<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Applicants - Admin Dashboard</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Dashboard Styles */
        .dashboard-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 180px);
        }

        /* Sidebar Styles */
        .sidebar {
            background-color: #2c3e50;
            color: white;
            padding: 2rem 0;
            position: relative;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            padding: 0;
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: #34495e;
        }

        .sidebar-menu i {
            margin-right: 1rem;
            width: 20px;
            text-align: center;
        }

        .user-info {
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #34495e;
        }

        .user-name {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .user-role {
            font-size: 0.9rem;
            color: #bdc3c7;
        }

        /* Logout Button */
        .logout-button {
            margin-top: auto;
            padding: 1rem 2rem;
            background-color: #c0392b;
            color: white;
            border: none;
            cursor: pointer;
            width: 100%;
            text-align: left;
            display: flex;
            align-items: center;
        }

        .logout-button i {
            margin-right: 1rem;
        }

        /* Main Content */
        .main-content {
            padding: 2rem;
            background-color: #f8f9fa;
        }

        .page-title {
            margin-bottom: 2rem;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .page-title i {
            color: #007bff;
        }

        /* Stats Cards */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stats-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .stats-icon {
            font-size: 2rem;
            padding: 1rem;
            border-radius: 50%;
        }

        .stats-icon.total { background-color: #e3f2fd; color: #1976d2; }
        .stats-icon.active { background-color: #e8f5e8; color: #388e3c; }
        .stats-icon.inactive { background-color: #ffebee; color: #d32f2f; }

        .stats-text h3 {
            margin: 0;
            font-size: 1.5rem;
        }

        .stats-text p {
            margin: 0.25rem 0 0;
            color: #666;
            font-size: 0.9rem;
        }

        /* Applicants Table */
        .applicants-section {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .table-container {
            overflow-x: auto;
            overflow-y: auto;
            max-height: 600px;
        }

        /* Scrollbar Styling */
        .table-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Ensure main content has proper height */
        .main-content {
            padding: 2rem;
            background-color: #f8f9fa;
            min-height: calc(100vh - 180px);
            overflow-y: auto;
        }

        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
        }

        .applicants-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px;
        }

        .applicants-table th,
        .applicants-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .applicants-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .status-active { background-color: #d4edda; color: #155724; }
        .status-inactive { background-color: #f8d7da; color: #721c24; }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn-view, .btn-toggle {
            padding: 0.25rem 0.75rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
        }

        .btn-view { background-color: #007bff; color: white; }
        .btn-toggle { background-color: #ffc107; color: #212529; }

        .btn-view:hover { background-color: #0056b3; }
        .btn-toggle:hover { background-color: #e0a800; }

        /* Search and Filter */
        .search-container {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .search-container input {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
        }

        .filter-select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 1.5rem;
            padding: 1rem;
        }

        .pagination button {
            padding: 0.5rem 1rem;
            margin: 0 0.25rem;
            border: 1px solid #ddd;
            background-color: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo-container">
                <img src="../images/awt-logo.png" alt="Access Welfare Trust Logo" class="logo">
                <div>
                    <h1>Access Welfare Trust</h1>
                    <p class="tagline">Connect, Empower & Sustain</p>
                </div>
            </div>
            <div class="header-right">
                <a href="dashboard.html" class="admin-link active">Admin Dashboard</a>
            </div>
        </div>
    </header>

    <div class="dashboard-container">
        <aside class="sidebar">
            <div class="user-info">
                <div class="user-name">Admin User</div>
                <div class="user-role">Administrator</div>
            </div>

            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="applications.html"><i class="fas fa-file-alt"></i> Applications</a></li>
                <li><a href="approved.html"><i class="fas fa-check-circle"></i> Approved</a></li>
                <li><a href="rejected.html"><i class="fas fa-times-circle"></i> Rejected</a></li>
                <li><a href="applicants.html" class="active"><i class="fas fa-users"></i> Applicants</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> Reports</a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> Settings</a></li>
            </ul>

            <button id="logoutButton" class="logout-button">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </aside>

        <main class="main-content">
            <h1 class="page-title">
                <i class="fas fa-users"></i>
                Applicants Management
            </h1>

            <!-- Stats Cards -->
            <div class="stats-container">
                <div class="stats-card">
                    <div class="stats-icon total">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stats-text">
                        <h3 id="totalUsers">0</h3>
                        <p>Total Applicants</p>
                    </div>
                </div>
                <div class="stats-card">
                    <div class="stats-icon active">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stats-text">
                        <h3 id="activeUsers">0</h3>
                        <p>Active Users</p>
                    </div>
                </div>
                <div class="stats-card">
                    <div class="stats-icon inactive">
                        <i class="fas fa-user-times"></i>
                    </div>
                    <div class="stats-text">
                        <h3 id="inactiveUsers">0</h3>
                        <p>Inactive Users</p>
                    </div>
                </div>
            </div>

            <!-- Applicants Table -->
            <div class="applicants-section">
                <div class="section-header">
                    <h2>All Applicants</h2>
                    <div class="search-container">
                        <select id="statusFilter" class="filter-select">
                            <option value="">All Status</option>
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                        <input type="text" id="searchInput" placeholder="Search applicants...">
                    </div>
                </div>

                <div class="table-container">
                    <table class="applicants-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Registration Date</th>
                                <th>Applications</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="applicantsTableBody">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <button class="active">1</button>
                    <button>2</button>
                    <button>3</button>
                    <button>4</button>
                    <button>5</button>
                </div>
            </div>
        </main>
    </div>

    <footer>
        <p>&copy; 2025 Access Welfare Trust. All rights reserved.</p>
    </footer>

    <script src="../js/api-config.js"></script>
    <script>
        let applicants = [];
        let filteredApplicants = [];

        document.addEventListener('DOMContentLoaded', function() {
            // Check admin authentication
            const authToken = localStorage.getItem('authToken');
            if (!authToken) {
                alert('Please login as admin to access this page');
                window.location.href = '../login-signup.html';
                return;
            }

            loadApplicants();
            setupEventListeners();
        });

        function setupEventListeners() {
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');
            
            if (searchInput) {
                searchInput.addEventListener('input', debounce(filterApplicants, 300));
            }
            
            if (statusFilter) {
                statusFilter.addEventListener('change', filterApplicants);
            }

            // Logout functionality
            const logoutButton = document.getElementById('logoutButton');
            if (logoutButton) {
                logoutButton.addEventListener('click', async function() {
                    if (confirm('Are you sure you want to logout?')) {
                        try {
                            const authToken = localStorage.getItem('authToken');
                            if (authToken) {
                                await fetch('http://localhost:3000/api/auth/logout', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': `Bearer ${authToken}`
                                    }
                                });
                            }
                        } catch (error) {
                            console.error('Logout API call failed:', error);
                        }

                        localStorage.removeItem('authToken');
                        localStorage.removeItem('adminLoggedIn');
                        window.location.href = '../login-signup.html';
                    }
                });
            }
        }

        async function loadApplicants() {
            try {
                const authToken = localStorage.getItem('authToken');
                
                // Load users (applicants)
                const usersResponse = await apiCall('/api/admin/users', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (usersResponse.ok) {
                    const result = await usersResponse.json();
                    applicants = result.data.users || [];
                    filteredApplicants = [...applicants];
                    
                    updateStats();
                    displayApplicants(filteredApplicants);
                } else {
                    // Fallback: create sample data
                    createSampleApplicants();
                }
            } catch (error) {
                console.error('Error loading applicants:', error);
                createSampleApplicants();
            }
        }

        function createSampleApplicants() {
            // Create sample data when API is not available
            applicants = [
                { id: 1, name: 'John Doe', email: '<EMAIL>', phone: '+91 9876543210', created_at: '2025-01-01', is_active: 1, application_count: 2 },
                { id: 2, name: 'Jane Smith', email: '<EMAIL>', phone: '+91 9876543211', created_at: '2025-01-02', is_active: 1, application_count: 1 },
                { id: 3, name: 'Bob Johnson', email: '<EMAIL>', phone: '+91 9876543212', created_at: '2025-01-03', is_active: 0, application_count: 0 }
            ];
            filteredApplicants = [...applicants];
            updateStats();
            displayApplicants(filteredApplicants);
        }

        function updateStats() {
            const total = applicants.length;
            const active = applicants.filter(user => user.is_active === 1).length;
            const inactive = total - active;

            document.getElementById('totalUsers').textContent = total;
            document.getElementById('activeUsers').textContent = active;
            document.getElementById('inactiveUsers').textContent = inactive;
        }

        function displayApplicants(users) {
            const tableBody = document.getElementById('applicantsTableBody');
            
            if (users.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #666;">No applicants found.</td></tr>';
                return;
            }

            tableBody.innerHTML = '';
            users.forEach(user => {
                const row = document.createElement('tr');
                
                const statusClass = user.is_active === 1 ? 'status-active' : 'status-inactive';
                const statusText = user.is_active === 1 ? 'Active' : 'Inactive';
                const toggleText = user.is_active === 1 ? 'Deactivate' : 'Activate';

                row.innerHTML = `
                    <td>${user.id}</td>
                    <td>${user.name || 'N/A'}</td>
                    <td>${user.email || 'N/A'}</td>
                    <td>${user.phone || 'N/A'}</td>
                    <td>${user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}</td>
                    <td>${user.application_count || 0}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td class="action-buttons">
                        <button class="btn-view" onclick="viewApplicant(${user.id})">View</button>
                        <button class="btn-toggle" onclick="toggleUserStatus(${user.id}, ${user.is_active})">${toggleText}</button>
                    </td>
                `;

                tableBody.appendChild(row);
            });
        }

        function filterApplicants() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;

            filteredApplicants = applicants.filter(user => {
                const matchesSearch = !searchTerm || 
                    (user.name && user.name.toLowerCase().includes(searchTerm)) ||
                    (user.email && user.email.toLowerCase().includes(searchTerm)) ||
                    (user.phone && user.phone.toLowerCase().includes(searchTerm));

                const matchesStatus = !statusFilter || user.is_active.toString() === statusFilter;

                return matchesSearch && matchesStatus;
            });

            displayApplicants(filteredApplicants);
        }

        function viewApplicant(userId) {
            const user = applicants.find(u => u.id === userId);
            if (user) {
                alert(`Applicant Details:\n\nID: ${user.id}\nName: ${user.name}\nEmail: ${user.email}\nPhone: ${user.phone}\nRegistered: ${new Date(user.created_at).toLocaleDateString()}\nApplications: ${user.application_count || 0}\nStatus: ${user.is_active ? 'Active' : 'Inactive'}`);
            }
        }

        async function toggleUserStatus(userId, currentStatus) {
            const newStatus = currentStatus === 1 ? 0 : 1;
            const action = newStatus === 1 ? 'activate' : 'deactivate';
            
            if (!confirm(`Are you sure you want to ${action} this user?`)) {
                return;
            }

            try {
                const authToken = localStorage.getItem('authToken');
                const response = await apiCall(`/api/admin/users/${userId}/status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ is_active: newStatus })
                });

                if (response.ok) {
                    alert(`User ${action}d successfully!`);
                    loadApplicants();
                } else {
                    alert(`Failed to ${action} user`);
                }
            } catch (error) {
                console.error('Error updating user status:', error);
                alert('Error updating user status');
            }
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html>
