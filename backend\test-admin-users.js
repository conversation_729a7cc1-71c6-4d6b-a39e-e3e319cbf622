const { executeQuery } = require('./config/database');

async function checkAdminUsers() {
    try {
        console.log('🔍 Checking admin users...');
        
        const adminUsers = await executeQuery(
            'SELECT id, name, email, role FROM users WHERE role = ?',
            ['admin']
        );
        
        console.log('📋 Admin users found:');
        adminUsers.forEach(user => {
            console.log(`- ID: ${user.id}, Name: ${user.name}, Email: ${user.email}, Role: ${user.role}`);
        });
        
        if (adminUsers.length === 0) {
            console.log('❌ No admin users found!');
        }
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Error:', error);
        process.exit(1);
    }
}

checkAdminUsers();
