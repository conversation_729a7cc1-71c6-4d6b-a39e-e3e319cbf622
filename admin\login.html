<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Access Welfare Trust</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .login-container {
            max-width: 400px;
            margin: 3rem auto;
            padding: 2rem;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .login-title {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--primary-color);
        }

        .login-form {
            display: flex;
            flex-direction: column;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input {
            padding: 0.8rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            width: 100%;
        }

        .login-button {
            background-color: var(--primary-color);
            color: white;
            padding: 0.8rem;
            border: none;
            border-radius: 4px;
            font-weight: bold;
            cursor: pointer;
            margin-top: 1rem;
        }

        .login-button:hover {
            background-color: #3a6347;
        }

        .error-message {
            color: var(--error-color);
            margin-top: 1rem;
            text-align: center;
            display: none;
        }

        .forgot-password {
            text-align: center;
            margin-top: 1rem;
        }

        .forgot-password a {
            color: var(--accent-color);
            text-decoration: none;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo-container">
                <img src="../images/awt-logo.png" alt="Access Welfare Trust Logo" class="logo">
                <div>
                    <h1>Access Welfare Trust</h1>
                    <p class="tagline">Connect, Empower & Sustain</p>
                </div>
            </div>
            <div class="header-right">
                <a href="login.html" class="admin-link active">Admin Login</a>
            </div>
        </div>
    </header>

    <main>
        <div class="login-container">
            <h2 class="login-title">Admin Login</h2>

            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <button type="submit" class="login-button">Login</button>

                <div id="errorMessage" class="error-message">
                    Login failed. Please try again.
                </div>

                <div class="forgot-password">
                    <a href="#">Forgot Password?</a>
                </div>
            </form>
        </div>
    </main>

    <footer>
        <p>&copy; 2025 Access Welfare Trust. All rights reserved.</p>
    </footer>

    <script src="../js/api-config.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const errorMessage = document.getElementById('errorMessage');
            const loginButton = document.querySelector('.login-button');

            if (loginForm) {
                loginForm.addEventListener('submit', async function(e) {
                    e.preventDefault();

                    const email = document.getElementById('email').value;
                    const password = document.getElementById('password').value;

                    // Show loading state
                    loginButton.textContent = 'Logging in...';
                    loginButton.disabled = true;
                    errorMessage.style.display = 'none';

                    try {
                        // Call backend login API
                        const response = await apiCall('/api/auth/login', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                email: email,
                                password: password
                            })
                        });

                        if (response.ok) {
                            const result = await response.json();

                            // Check if user has admin role
                            if (result.data.user.role === 'admin') {
                                // Store authentication data
                                localStorage.setItem('authToken', result.data.token);
                                localStorage.setItem('adminLoggedIn', 'true');
                                localStorage.setItem('userRole', 'admin');

                                // Redirect to admin dashboard
                                window.location.href = 'dashboard.html';
                            } else {
                                // User is not an admin
                                errorMessage.textContent = 'Access denied. Admin privileges required.';
                                errorMessage.style.display = 'block';
                            }
                        } else {
                            // Login failed
                            const error = await response.json();
                            errorMessage.textContent = error.message || 'Invalid email or password.';
                            errorMessage.style.display = 'block';
                        }
                    } catch (error) {
                        console.error('Login error:', error);
                        errorMessage.textContent = 'Login failed. Please check your connection and try again.';
                        errorMessage.style.display = 'block';
                    } finally {
                        // Reset button state
                        loginButton.textContent = 'Login';
                        loginButton.disabled = false;

                        // Clear password field on error
                        if (errorMessage.style.display === 'block') {
                            document.getElementById('password').value = '';
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>
