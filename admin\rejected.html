<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rejected Applications - Admin Dashboard</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Dashboard Styles */
        .dashboard-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 180px);
        }

        /* Sidebar Styles */
        .sidebar {
            background-color: #2c3e50;
            color: white;
            padding: 2rem 0;
            position: relative;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            padding: 0;
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: #34495e;
        }

        .sidebar-menu i {
            margin-right: 1rem;
            width: 20px;
            text-align: center;
        }

        .user-info {
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #34495e;
        }

        .user-name {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .user-role {
            font-size: 0.9rem;
            color: #bdc3c7;
        }

        /* Logout Button */
        .logout-button {
            margin-top: auto;
            padding: 1rem 2rem;
            background-color: #c0392b;
            color: white;
            border: none;
            cursor: pointer;
            width: 100%;
            text-align: left;
            display: flex;
            align-items: center;
        }

        .logout-button i {
            margin-right: 1rem;
        }

        /* Main Content */
        .main-content {
            padding: 2rem;
            background-color: #f8f9fa;
        }

        .page-title {
            margin-bottom: 2rem;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .page-title i {
            color: #dc3545;
        }

        /* Stats Card */
        .stats-card {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .stats-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stats-text h2 {
            margin: 0;
            font-size: 2.5rem;
        }

        .stats-text p {
            margin: 0.5rem 0 0;
            opacity: 0.9;
        }

        .stats-icon {
            font-size: 3rem;
            opacity: 0.8;
        }

        /* Applications Table */
        .applications-section {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .table-container {
            overflow-x: auto;
            overflow-y: auto;
            max-height: 600px;
        }

        /* Scrollbar Styling */
        .table-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Ensure main content has proper height */
        .main-content {
            padding: 2rem;
            background-color: #f8f9fa;
            min-height: calc(100vh - 180px);
            overflow-y: auto;
        }

        .section-header {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
        }

        .applications-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 900px;
        }

        .applications-table th,
        .applications-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #eee;
        }

        .applications-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            background-color: #f8d7da;
            color: #721c24;
        }

        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn-view, .btn-reconsider {
            padding: 0.25rem 0.75rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
        }

        .btn-view { 
            background-color: #007bff; 
            color: white; 
        }

        .btn-reconsider { 
            background-color: #ffc107; 
            color: #212529; 
        }

        .btn-view:hover { background-color: #0056b3; }
        .btn-reconsider:hover { background-color: #e0a800; }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(2px);
        }

        .modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .modal-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 1.5rem 2rem;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .close {
            color: white;
            font-size: 2rem;
            font-weight: bold;
            cursor: pointer;
            border: none;
            background: none;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .close:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 2rem;
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .detail-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            border-left: 4px solid #dc3545;
        }

        .section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .detail-item {
            display: flex;
            margin-bottom: 0.75rem;
            align-items: flex-start;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
            min-width: 140px;
            margin-right: 1rem;
        }

        .detail-value {
            color: #2c3e50;
            flex: 1;
            word-break: break-word;
        }

        .status-badge-modal {
            display: inline-block;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            background-color: #f8d7da;
            color: #721c24;
        }

        .amount-highlight {
            font-size: 1.2rem;
            font-weight: bold;
            color: #28a745;
        }

        .rejection-reason-highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 1rem;
            margin-top: 0.5rem;
            font-style: italic;
            color: #856404;
        }

        .modal-footer {
            background: #f8f9fa;
            padding: 1rem 2rem;
            border-radius: 0 0 12px 12px;
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            border-top: 1px solid #dee2e6;
        }

        .btn-modal {
            padding: 0.5rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }

        .btn-reconsider-modal {
            background-color: #ffc107;
            color: #212529;
        }

        .btn-reconsider-modal:hover {
            background-color: #e0a800;
        }

        .btn-close-modal {
            background-color: #6c757d;
            color: white;
        }

        .btn-close-modal:hover {
            background-color: #545b62;
        }

        .documents-section {
            grid-column: 1 / -1;
        }

        .document-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 0.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .document-name {
            font-weight: 500;
            color: #495057;
        }

        .document-actions {
            display: flex;
            gap: 0.5rem;
        }

        .btn-download-doc {
            background-color: #17a2b8;
            color: white;
            border: none;
            padding: 0.3rem 0.8rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
        }

        .btn-download-doc:hover {
            background-color: #138496;
        }

        /* Search and Filter */
        .search-container {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .search-container input {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
        }

        .export-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .export-btn:hover {
            background-color: #c82333;
        }

        /* Rejection Reason */
        .rejection-reason {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 1.5rem;
            padding: 1rem;
        }

        .pagination button {
            padding: 0.5rem 1rem;
            margin: 0 0.25rem;
            border: 1px solid #ddd;
            background-color: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo-container">
                <img src="../images/awt-logo.png" alt="Access Welfare Trust Logo" class="logo">
                <div>
                    <h1>Access Welfare Trust</h1>
                    <p class="tagline">Connect, Empower & Sustain</p>
                </div>
            </div>
            <div class="header-right">
                <nav class="nav-links">
                    <a href="../index.html">Home</a>
                    <a href="../about.html">About Us</a>
                    <a href="../eligibility.html">Eligibility</a>
                    <a href="../application.html">Apply</a>
                </nav>
                <a href="dashboard.html" class="admin-link active">Admin Dashboard</a>
            </div>
        </div>
    </header>

    <div class="dashboard-container">
        <aside class="sidebar">
            <div class="user-info">
                <div class="user-name">Admin User</div>
                <div class="user-role">Administrator</div>
            </div>

            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="applications.html"><i class="fas fa-file-alt"></i> Applications</a></li>
                <li><a href="approved.html"><i class="fas fa-check-circle"></i> Approved</a></li>
                <li><a href="rejected.html" class="active"><i class="fas fa-times-circle"></i> Rejected</a></li>
                <li><a href="applicants.html"><i class="fas fa-users"></i> Applicants</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> Reports</a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> Settings</a></li>
            </ul>

            <button id="logoutButton" class="logout-button">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </aside>

        <main class="main-content">
            <h1 class="page-title">
                <i class="fas fa-times-circle"></i>
                Rejected Applications
            </h1>

            <!-- Stats Card -->
            <div class="stats-card">
                <div class="stats-content">
                    <div class="stats-text">
                        <h2 id="rejectedCount">0</h2>
                        <p>Total Rejected Applications</p>
                        <small id="rejectionRate">0% rejection rate</small>
                    </div>
                    <div class="stats-icon">
                        <i class="fas fa-times-circle"></i>
                    </div>
                </div>
            </div>

            <!-- Applications Table -->
            <div class="applications-section">
                <div class="section-header">
                    <h2>Rejected Applications</h2>
                    <div class="search-container">
                        <input type="text" id="searchInput" placeholder="Search rejected applications...">
                        <button class="export-btn" onclick="exportRejected()">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="applications-table">
                        <thead>
                            <tr>
                                <th>Application ID</th>
                                <th>Applicant Name</th>
                                <th>Course</th>
                                <th>Amount</th>
                                <th>Rejected Date</th>
                                <th>Reason</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="applicationsTableBody">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>

                <div class="pagination">
                    <button class="active">1</button>
                    <button>2</button>
                    <button>3</button>
                    <button>4</button>
                    <button>5</button>
                </div>
            </div>
        </main>
    </div>

    <!-- Application Details Modal -->
    <div id="applicationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Rejected Application Details</h2>
                <button class="close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="modalContent">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
            <div class="modal-footer">
                <button id="reconsiderBtn" class="btn-modal btn-reconsider-modal" onclick="reconsiderFromModal()">
                    <i class="fas fa-redo"></i> Reconsider
                </button>
                <button class="btn-modal btn-close-modal" onclick="closeModal()">
                    <i class="fas fa-times"></i> Close
                </button>
            </div>
        </div>
    </div>

    <footer>
        <p>&copy; 2025 Access Welfare Trust. All rights reserved.</p>
    </footer>

    <script src="../js/api-config.js"></script>
    <script>
        let rejectedApplications = [];

        document.addEventListener('DOMContentLoaded', function() {
            // Check admin authentication
            const authToken = localStorage.getItem('authToken');
            if (!authToken) {
                alert('Please login as admin to access this page');
                window.location.href = '../login-signup.html';
                return;
            }

            loadRejectedApplications();
            setupEventListeners();
        });

        function setupEventListeners() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', debounce(filterApplications, 300));
            }

            // Logout functionality
            const logoutButton = document.getElementById('logoutButton');
            if (logoutButton) {
                logoutButton.addEventListener('click', async function() {
                    if (confirm('Are you sure you want to logout?')) {
                        try {
                            const authToken = localStorage.getItem('authToken');
                            if (authToken) {
                                await fetch('http://localhost:3000/api/auth/logout', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': `Bearer ${authToken}`
                                    }
                                });
                            }
                        } catch (error) {
                            console.error('Logout API call failed:', error);
                        }

                        localStorage.removeItem('authToken');
                        localStorage.removeItem('adminLoggedIn');
                        window.location.href = '../login-signup.html';
                    }
                });
            }
        }

        async function loadRejectedApplications() {
            try {
                const authToken = localStorage.getItem('authToken');
                const response = await apiCall('/api/admin/applications', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    const allApplications = result.data.applications || [];
                    
                    // Filter only rejected applications
                    rejectedApplications = allApplications.filter(app => app.status === 'rejected');
                    
                    updateStats(allApplications.length, rejectedApplications.length);
                    displayApplications(rejectedApplications);
                } else {
                    throw new Error('Failed to load applications');
                }
            } catch (error) {
                console.error('Error loading applications:', error);
                document.getElementById('applicationsTableBody').innerHTML = 
                    '<tr><td colspan="9" style="text-align: center; color: #666;">Failed to load rejected applications. Please try again.</td></tr>';
            }
        }

        function updateStats(totalApps, rejectedCount) {
            document.getElementById('rejectedCount').textContent = rejectedCount;
            const rejectionRate = totalApps > 0 ? ((rejectedCount / totalApps) * 100).toFixed(1) : 0;
            document.getElementById('rejectionRate').textContent = `${rejectionRate}% rejection rate`;
        }

        function displayApplications(apps) {
            const tableBody = document.getElementById('applicationsTableBody');
            
            if (apps.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #666;">No rejected applications found.</td></tr>';
                return;
            }

            tableBody.innerHTML = '';
            apps.forEach(app => {
                const row = document.createElement('tr');

                row.innerHTML = `
                    <td>${app.application_id || app.id}</td>
                    <td>${app.name || 'N/A'}</td>
                    <td>${app.current_course || 'N/A'}</td>
                    <td>₹${app.scholarship_amount_figures ? app.scholarship_amount_figures.toLocaleString() : 'N/A'}</td>
                    <td>${app.updated_at ? new Date(app.updated_at).toLocaleDateString() : 'N/A'}</td>
                    <td class="rejection-reason" title="${app.admin_comments || 'No reason provided'}">${app.admin_comments || 'No reason provided'}</td>
                    <td><span class="status-badge">Rejected</span></td>
                    <td class="action-buttons">
                        <button class="btn-view" onclick="viewApplication(${app.id})">View</button>
                        <button class="btn-reconsider" onclick="reconsiderApplication(${app.id})">Reconsider</button>
                    </td>
                `;

                tableBody.appendChild(row);
            });
        }

        function filterApplications() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            
            const filtered = rejectedApplications.filter(app => {
                return !searchTerm || 
                    (app.name && app.name.toLowerCase().includes(searchTerm)) ||
                    (app.user_email && app.user_email.toLowerCase().includes(searchTerm)) ||
                    (app.application_id && app.application_id.toLowerCase().includes(searchTerm)) ||
                    (app.admin_notes && app.admin_notes.toLowerCase().includes(searchTerm));
            });

            displayApplications(filtered);
        }

        async function viewApplication(applicationId) {
            try {
                const authToken = localStorage.getItem('authToken');
                const response = await apiCall(`/api/admin/applications/${applicationId}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    showApplicationDetails(result.data.application);
                } else {
                    alert('Failed to load application details');
                }
            } catch (error) {
                console.error('Error loading application details:', error);
                alert('Error loading application details');
            }
        }

        let currentApplicationId = null;

        function showApplicationDetails(app) {
            console.log('🎯 showApplicationDetails called with app:', app);
            console.log('📊 Application fields count:', Object.keys(app).length);

            currentApplicationId = app.id;

            const modalContent = document.getElementById('modalContent');
            const statusClass = `status-${app.status || 'rejected'}`;
            const statusText = (app.status || 'rejected').replace('_', ' ').toUpperCase();

            modalContent.innerHTML = `
                <div class="details-grid">
                    <!-- Rejection Information -->
                    <div class="detail-section">
                        <h3 class="section-title">
                            <i class="fas fa-times-circle"></i>
                            Rejection Information
                        </h3>
                        <div class="detail-item">
                            <span class="detail-label">Application ID:</span>
                            <span class="detail-value">${app.application_id || app.id}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Status:</span>
                            <span class="detail-value">
                                <span class="status-badge-modal ${statusClass}">${statusText}</span>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Submitted Date:</span>
                            <span class="detail-value">${app.created_at ? new Date(app.created_at).toLocaleDateString() : 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Rejected Date:</span>
                            <span class="detail-value">${app.updated_at ? new Date(app.updated_at).toLocaleDateString() : 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Rejection Reason:</span>
                            <div class="detail-value">
                                <div class="rejection-reason-highlight">
                                    ${app.admin_notes || 'No reason provided'}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Personal Details -->
                    <div class="detail-section">
                        <h3 class="section-title">
                            <i class="fas fa-user"></i>
                            Step 1: Personal Details
                        </h3>
                        <div class="detail-item">
                            <span class="detail-label">1. Name:</span>
                            <span class="detail-value">${app.name || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">2. Gender:</span>
                            <span class="detail-value">${app.gender || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">3. Date of Birth:</span>
                            <span class="detail-value">${app.date_of_birth ? new Date(app.date_of_birth).toLocaleDateString() : 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Age:</span>
                            <span class="detail-value">${app.age || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">4. Place of Birth:</span>
                            <span class="detail-value">${app.place_of_birth || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">5. Marital Status:</span>
                            <span class="detail-value">${app.marital_status || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">6. Religion:</span>
                            <span class="detail-value">${app.religion || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">7. Category:</span>
                            <span class="detail-value">${app.category || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">8. Nationality:</span>
                            <span class="detail-value">${app.nationality || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">9. Present Address:</span>
                            <span class="detail-value">${app.present_address || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Present State:</span>
                            <span class="detail-value">${app.present_state || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Present Country:</span>
                            <span class="detail-value">${app.present_country || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">10. Permanent Address:</span>
                            <span class="detail-value">${app.permanent_address || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Permanent State:</span>
                            <span class="detail-value">${app.permanent_state || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Permanent Country:</span>
                            <span class="detail-value">${app.permanent_country || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">11. Phone:</span>
                            <span class="detail-value">${app.phone || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">12. WhatsApp:</span>
                            <span class="detail-value">${app.whatsapp || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">13. Email:</span>
                            <span class="detail-value">${app.email || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">16. Disability Status:</span>
                            <span class="detail-value">${app.is_disabled ? 'Yes' : 'No'}</span>
                        </div>
                        ${app.is_disabled ? `
                        <div class="detail-item">
                            <span class="detail-label">Disability Type:</span>
                            <span class="detail-value">${app.disability_type || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Disability Percentage:</span>
                            <span class="detail-value">${app.disability_percentage ? app.disability_percentage + '%' : 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Disability Description:</span>
                            <span class="detail-value">${app.disability_description || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Issuing Authority:</span>
                            <span class="detail-value">${app.issuing_authority || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Certificate Number:</span>
                            <span class="detail-value">${app.certificate_number || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Issue Date:</span>
                            <span class="detail-value">${app.issue_date || 'N/A'}</span>
                        </div>
                        ` : ''}
                    </div>

                    <!-- Step 2: Family Details -->
                    <div class="detail-section">
                        <h3 class="section-title">
                            <i class="fas fa-users"></i>
                            Step 2: Family Details
                        </h3>
                        <div class="detail-item">
                            <span class="detail-label">14. Father's Name:</span>
                            <span class="detail-value">${app.father_name || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Father's Age:</span>
                            <span class="detail-value">${app.father_age || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Father's Occupation:</span>
                            <span class="detail-value">${app.father_occupation || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Father's Income:</span>
                            <span class="detail-value">₹${app.father_income ? app.father_income.toLocaleString() : 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Father's Employment:</span>
                            <span class="detail-value">${app.father_employment || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Mother's Name:</span>
                            <span class="detail-value">${app.mother_name || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Mother's Age:</span>
                            <span class="detail-value">${app.mother_age || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Mother's Occupation:</span>
                            <span class="detail-value">${app.mother_occupation || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Mother's Income:</span>
                            <span class="detail-value">₹${app.mother_income ? app.mother_income.toLocaleString() : 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Mother's Employment:</span>
                            <span class="detail-value">${app.mother_employment || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">15. Family Total Income:</span>
                            <span class="detail-value amount-highlight">₹${app.family_total_income ? app.family_total_income.toLocaleString() : 'N/A'}</span>
                        </div>
                    </div>

                    <!-- Step 3: Educational Details -->
                    <div class="detail-section">
                        <h3 class="section-title">
                            <i class="fas fa-graduation-cap"></i>
                            Step 3: Educational Details
                        </h3>
                        ${app.sslc_institution ? `
                        <h4 style="color: #667eea; margin: 1rem 0 0.5rem 0;">SSLC/10th Standard</h4>
                        <div class="detail-item">
                            <span class="detail-label">Institution:</span>
                            <span class="detail-value">${app.sslc_institution}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Type:</span>
                            <span class="detail-value">${app.sslc_type || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Board:</span>
                            <span class="detail-value">${app.sslc_board || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Marks:</span>
                            <span class="detail-value">${app.sslc_marks || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Year:</span>
                            <span class="detail-value">${app.sslc_year || 'N/A'}</span>
                        </div>
                        ` : ''}

                        ${app.hsc_institution ? `
                        <h4 style="color: #667eea; margin: 1rem 0 0.5rem 0;">HSC/12th Standard</h4>
                        <div class="detail-item">
                            <span class="detail-label">Institution:</span>
                            <span class="detail-value">${app.hsc_institution}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Type:</span>
                            <span class="detail-value">${app.hsc_type || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Board:</span>
                            <span class="detail-value">${app.hsc_board || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Marks:</span>
                            <span class="detail-value">${app.hsc_marks || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Year:</span>
                            <span class="detail-value">${app.hsc_year || 'N/A'}</span>
                        </div>
                        ` : ''}

                        ${app.ug_institution ? `
                        <h4 style="color: #667eea; margin: 1rem 0 0.5rem 0;">Under Graduate</h4>
                        <div class="detail-item">
                            <span class="detail-label">Institution:</span>
                            <span class="detail-value">${app.ug_institution}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Type:</span>
                            <span class="detail-value">${app.ug_type || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Board/University:</span>
                            <span class="detail-value">${app.ug_board || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Marks:</span>
                            <span class="detail-value">${app.ug_marks || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Year:</span>
                            <span class="detail-value">${app.ug_year || 'N/A'}</span>
                        </div>
                        ` : ''}

                        ${app.diploma_institution ? `
                        <h4 style="color: #667eea; margin: 1rem 0 0.5rem 0;">Diploma</h4>
                        <div class="detail-item">
                            <span class="detail-label">Institution:</span>
                            <span class="detail-value">${app.diploma_institution}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Type:</span>
                            <span class="detail-value">${app.diploma_type || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Board:</span>
                            <span class="detail-value">${app.diploma_board || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Marks:</span>
                            <span class="detail-value">${app.diploma_marks || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Year:</span>
                            <span class="detail-value">${app.diploma_year || 'N/A'}</span>
                        </div>
                        ` : ''}

                        ${app.vocational_institution ? `
                        <h4 style="color: #667eea; margin: 1rem 0 0.5rem 0;">Vocational</h4>
                        <div class="detail-item">
                            <span class="detail-label">Institution:</span>
                            <span class="detail-value">${app.vocational_institution}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Type:</span>
                            <span class="detail-value">${app.vocational_type || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Board:</span>
                            <span class="detail-value">${app.vocational_board || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Marks:</span>
                            <span class="detail-value">${app.vocational_marks || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Year:</span>
                            <span class="detail-value">${app.vocational_year || 'N/A'}</span>
                        </div>
                        ` : ''}

                        ${app.others_institution ? `
                        <h4 style="color: #667eea; margin: 1rem 0 0.5rem 0;">Others</h4>
                        <div class="detail-item">
                            <span class="detail-label">Institution:</span>
                            <span class="detail-value">${app.others_institution}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Type:</span>
                            <span class="detail-value">${app.others_type || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Board:</span>
                            <span class="detail-value">${app.others_board || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Marks:</span>
                            <span class="detail-value">${app.others_marks || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Year:</span>
                            <span class="detail-value">${app.others_year || 'N/A'}</span>
                        </div>
                        ` : ''}

                        <h4 style="color: #667eea; margin: 1rem 0 0.5rem 0;">Current Studies</h4>
                        <div class="detail-item">
                            <span class="detail-label">Current Course:</span>
                            <span class="detail-value">${app.current_course || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Course Duration:</span>
                            <span class="detail-value">${app.course_duration || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Course Year:</span>
                            <span class="detail-value">${app.course_year || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Roll Number:</span>
                            <span class="detail-value">${app.roll_number || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Institution Name:</span>
                            <span class="detail-value">${app.institution_name || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Institution Type:</span>
                            <span class="detail-value">${app.institution_type || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Institution Address:</span>
                            <span class="detail-value">${app.institution_address || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Institution Phone:</span>
                            <span class="detail-value">${app.institution_phone || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Institution Email:</span>
                            <span class="detail-value">${app.institution_email || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Institution Website:</span>
                            <span class="detail-value">${app.institution_website || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Term Fees:</span>
                            <span class="detail-value">${app.term_fees || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Tuition Fees:</span>
                            <span class="detail-value">₹${app.tuition_fees ? app.tuition_fees.toLocaleString() : 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Other Fees:</span>
                            <span class="detail-value">₹${app.other_fees ? app.other_fees.toLocaleString() : 'N/A'}</span>
                        </div>
                    </div>

                    <!-- Step 4: Scholarship Details -->
                    <div class="detail-section">
                        <h3 class="section-title">
                            <i class="fas fa-rupee-sign"></i>
                            Step 4: Scholarship Details
                        </h3>
                        <div class="detail-item">
                            <span class="detail-label">Scholarship Amount (Figures):</span>
                            <span class="detail-value amount-highlight">₹${app.scholarship_amount_figures ? app.scholarship_amount_figures.toLocaleString() : 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Amount in Words:</span>
                            <span class="detail-value">${app.scholarship_amount_words || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Previous AWT Scholarship:</span>
                            <span class="detail-value">${app.previous_awt_scholarship || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Other Scholarships:</span>
                            <span class="detail-value">${app.other_scholarships || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Applied Scholarships:</span>
                            <span class="detail-value">${app.applied_scholarships || 'N/A'}</span>
                        </div>
                    </div>

                    <!-- Step 5: References, Documents & Declaration -->
                    <div class="detail-section">
                        <h3 class="section-title">
                            <i class="fas fa-file-alt"></i>
                            Step 5: References, Documents & Declaration
                        </h3>

                        <h4 style="color: #667eea; margin: 1rem 0 0.5rem 0;">References</h4>
                        ${app.reference1_name ? `
                        <div class="detail-item">
                            <span class="detail-label">Reference 1 Name:</span>
                            <span class="detail-value">${app.reference1_name}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Reference 1 Phone:</span>
                            <span class="detail-value">${app.reference1_phone || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Reference 1 Email:</span>
                            <span class="detail-value">${app.reference1_email || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Reference 1 Position:</span>
                            <span class="detail-value">${app.reference1_position || 'N/A'}</span>
                        </div>
                        ` : ''}

                        ${app.reference2_name ? `
                        <div class="detail-item">
                            <span class="detail-label">Reference 2 Name:</span>
                            <span class="detail-value">${app.reference2_name}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Reference 2 Phone:</span>
                            <span class="detail-value">${app.reference2_phone || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Reference 2 Email:</span>
                            <span class="detail-value">${app.reference2_email || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Reference 2 Position:</span>
                            <span class="detail-value">${app.reference2_position || 'N/A'}</span>
                        </div>
                        ` : ''}

                        <h4 style="color: #667eea; margin: 1rem 0 0.5rem 0;">Additional Information</h4>
                        ${app.extracurricular ? `
                        <div class="detail-item">
                            <span class="detail-label">Extracurricular Activities:</span>
                            <span class="detail-value">${app.extracurricular}</span>
                        </div>
                        ` : ''}

                        ${app.goals ? `
                        <div class="detail-item">
                            <span class="detail-label">Goals and Aspirations:</span>
                            <span class="detail-value">${app.goals}</span>
                        </div>
                        ` : ''}

                        ${app.other_info ? `
                        <div class="detail-item">
                            <span class="detail-label">Other Information:</span>
                            <span class="detail-value">${app.other_info}</span>
                        </div>
                        ` : ''}

                        <h4 style="color: #667eea; margin: 1rem 0 0.5rem 0;">Declaration</h4>
                        <div class="detail-item">
                            <span class="detail-label">Declaration Place:</span>
                            <span class="detail-value">${app.declaration_place || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Declaration Date:</span>
                            <span class="detail-value">${app.declaration_date ? new Date(app.declaration_date).toLocaleDateString() : 'N/A'}</span>
                        </div>
                    </div>

                    <!-- Uploaded Documents -->
                    <div class="detail-section documents-section">
                        <h3 class="section-title">
                            <i class="fas fa-file-alt"></i>
                            Uploaded Documents
                        </h3>
                        ${app.file_uploads && app.file_uploads.length > 0 ?
                            getOrderedDocuments(app.file_uploads).map(file => `
                                <div class="document-item">
                                    <span class="document-name">
                                        <i class="fas fa-file-${getFileIcon(file.mime_type)}"></i>
                                        ${getFileDisplayName(file.file_type)} (${file.original_name})
                                    </span>
                                    <div class="document-actions">
                                        <button class="btn-download-doc" onclick="viewDocument(${file.id})">
                                            <i class="fas fa-eye"></i> View
                                        </button>
                                        <button class="btn-download-doc" onclick="downloadDocument(${file.id}, '${file.original_name}')">
                                            <i class="fas fa-download"></i> Download
                                        </button>
                                    </div>
                                </div>
                            `).join('') :
                            '<div class="document-item"><span style="color: #6c757d;">No documents uploaded</span></div>'
                        }
                    </div>
                </div>
            `;

            // Show modal
            document.getElementById('applicationModal').style.display = 'block';
        }

        async function reconsiderApplication(applicationId) {
            const newNotes = prompt('Please enter notes for reconsidering this application:', '');
            if (newNotes === null) return;

            if (!confirm('Are you sure you want to reconsider this application? It will be moved to under review status.')) {
                return;
            }

            try {
                const authToken = localStorage.getItem('authToken');
                const response = await apiCall(`/api/admin/applications/${applicationId}/review`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        status: 'under_review',
                        admin_notes: newNotes
                    })
                });

                if (response.ok) {
                    alert('Application moved to under review successfully!');
                    loadRejectedApplications();
                } else {
                    alert('Failed to update application status');
                }
            } catch (error) {
                console.error('Error updating status:', error);
                alert('Error updating application status');
            }
        }

        function closeModal() {
            document.getElementById('applicationModal').style.display = 'none';
            currentApplicationId = null;
        }

        function reconsiderFromModal() {
            if (currentApplicationId) {
                reconsiderApplication(currentApplicationId);
                closeModal();
            }
        }

        function getFileIcon(mimeType) {
            if (mimeType.includes('pdf')) return 'pdf';
            if (mimeType.includes('image')) return 'image';
            if (mimeType.includes('word')) return 'word';
            return 'file';
        }

        function getOrderedDocuments(files) {
            // Define the order as per application form sequence
            const documentOrder = [
                'photo',                    // 1. Personal Details - Photo
                'marksheets',              // 2. Educational Details - All Marksheets
                'fee_circular',            // 3. Scholarship Details - Fee Circular
                'signature',               // 4. Document Upload - Digital Signature
                'photo_id',                // 4. Document Upload - Photo ID
                'institute_id',            // 4. Document Upload - Institute ID Card
                'address_proof',           // 4. Document Upload - Address Proof
                'aadhaar_card',            // 4. Document Upload - Aadhaar Card
                'birth_certificate',       // 4. Document Upload - Birth Certificate
                'community_certificate',   // 4. Document Upload - Community Certificate
                'course_details',          // 4. Document Upload - Course Fee Details
                'income_certificate',      // 4. Document Upload - Income Certificate
                'attendance_certificate',  // 4. Document Upload - Attendance Certificate
                'fee_receipts',            // 4. Document Upload - Fee Receipts 2025-26
                'disability_certificate',  // Special - Disability Certificate
                'other'                    // Other documents
            ];

            // Sort files according to the defined order
            return files.sort((a, b) => {
                const orderA = documentOrder.indexOf(a.file_type);
                const orderB = documentOrder.indexOf(b.file_type);

                // If file type not found in order, put it at the end
                const finalOrderA = orderA === -1 ? documentOrder.length : orderA;
                const finalOrderB = orderB === -1 ? documentOrder.length : orderB;

                // If same type (like multiple marksheets), sort by upload time
                if (finalOrderA === finalOrderB) {
                    return new Date(a.uploaded_at) - new Date(b.uploaded_at);
                }

                return finalOrderA - finalOrderB;
            });
        }

        function getFileDisplayName(fileType) {
            const displayNames = {
                // Application form sequence
                'photo': '📸 Passport Size Photograph',
                'marksheets': '📚 All Marksheets',
                'fee_circular': '💸 Institute Official Fee Circular',
                'signature': '✍️ Digital Signature',
                'photo_id': '🆔 Photo ID',
                'institute_id': '🏫 Institute ID Card',
                'address_proof': '🏠 Address Proof',
                'aadhaar_card': '🆔 Aadhaar Card',
                'birth_certificate': '📜 Birth Certificate',
                'community_certificate': '👥 Community Certificate',
                'course_details': '💰 Course Fee Details',
                'income_certificate': '💵 Income Certificate',
                'attendance_certificate': '📅 Attendance Certificate',
                'fee_receipts': '🧾 Fee Receipts 2025-26',
                'disability_certificate': '♿ Disability Certificate',
                'other': '📄 Other Document',

                // Handle camelCase versions
                'photoID': '🆔 Photo ID',
                'instituteID': '🏫 Institute ID Card',
                'addressProof': '🏠 Address Proof',
                'aadhaarCard': '🆔 Aadhaar Card',
                'birthCertificate': '📜 Birth Certificate',
                'communityCertificate': '👥 Community Certificate',
                'courseDetails': '💰 Course Fee Details',
                'incomeCertificate': '💵 Income Certificate',
                'attendanceCertificate': '📅 Attendance Certificate',
                'feeReceipts': '🧾 Fee Receipts 2025-26',
                'disabilityCertificate': '♿ Disability Certificate',
                'feeCircular': '💸 Institute Official Fee Circular'
            };
            return displayNames[fileType] || fileType;
        }

        function viewDocument(fileId) {
            if (!fileId) {
                alert('Document not available');
                return;
            }

            // Open document in new tab for viewing (inline display)
            const authToken = localStorage.getItem('authToken');
            const url = `http://localhost:3000/api/files/view/${fileId}?token=${authToken}`;

            // Open in new tab - this will show the actual document content
            window.open(url, '_blank', 'width=1000,height=800,scrollbars=yes,resizable=yes');
        }

        function downloadDocument(fileId, fileName) {
            if (!fileId) {
                alert('Document not available');
                return;
            }

            const authToken = localStorage.getItem('authToken');

            // Use the download endpoint for forced download
            const link = document.createElement('a');
            link.href = `http://localhost:3000/api/files/download/${fileId}?token=${authToken}`;
            link.download = fileName || `document_${fileId}`;
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('applicationModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });

        function exportRejected() {
            if (rejectedApplications.length === 0) {
                alert('No rejected applications to export');
                return;
            }

            // Create CSV content
            const headers = ['Application ID', 'Name', 'Course', 'Amount', 'Rejected Date', 'Reason'];
            const csvContent = [
                headers.join(','),
                ...rejectedApplications.map(app => [
                    app.application_id || app.id,
                    app.name || '',
                    app.current_course || '',
                    app.scholarship_amount_figures || '',
                    app.updated_at ? new Date(app.updated_at).toLocaleDateString() : '',
                    `"${app.admin_notes || 'No reason provided'}"`
                ].join(','))
            ].join('\n');

            // Download CSV
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `rejected_applications_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>
</body>
</html>
