const mysql = require('mysql2/promise');
require('dotenv').config();

const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'trust_scholarship_db',
    charset: 'utf8mb4'
};

async function updateDatabase() {
    let connection;
    
    try {
        console.log('🔄 Connecting to database...');
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ Connected to database');

        // Add editable field to applications table
        console.log('🔄 Adding editable field to applications table...');
        try {
            await connection.execute(`
                ALTER TABLE applications 
                ADD COLUMN editable BOOLEAN DEFAULT FALSE AFTER admin_notes
            `);
            console.log('✅ Added editable field');
        } catch (error) {
            if (error.code === 'ER_DUP_FIELDNAME') {
                console.log('ℹ️ Editable field already exists');
            } else {
                throw error;
            }
        }

        // Update status enum
        console.log('🔄 Updating status enum...');
        try {
            await connection.execute(`
                ALTER TABLE applications 
                MODIFY COLUMN status ENUM('draft', 'submitted', 'pending', 'under_review', 'approved', 'rejected', 'resubmitted') DEFAULT 'draft'
            `);
            console.log('✅ Updated status enum');
        } catch (error) {
            console.log('ℹ️ Status enum update skipped:', error.message);
        }

        // Rename admin_notes to admin_comments
        console.log('🔄 Renaming admin_notes to admin_comments...');
        try {
            await connection.execute(`
                ALTER TABLE applications 
                CHANGE COLUMN admin_notes admin_comments TEXT
            `);
            console.log('✅ Renamed admin_notes to admin_comments');
        } catch (error) {
            if (error.code === 'ER_BAD_FIELD_ERROR') {
                console.log('ℹ️ admin_notes field does not exist or already renamed');
            } else {
                throw error;
            }
        }

        // Create system_settings table
        console.log('🔄 Creating system_settings table...');
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS system_settings (
                id INT PRIMARY KEY AUTO_INCREMENT,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT NOT NULL,
                setting_type ENUM('boolean', 'string', 'number', 'json') DEFAULT 'string',
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_setting_key (setting_key)
            )
        `);
        console.log('✅ Created system_settings table');

        // Insert default settings
        console.log('🔄 Inserting default settings...');
        await connection.execute(`
            INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
            ('application_form_enabled', 'true', 'boolean', 'Controls whether users can access and submit application forms'),
            ('application_deadline', '2025-12-31', 'string', 'Last date for application submissions'),
            ('max_applications_per_user', '1', 'number', 'Maximum number of applications a user can submit'),
            ('notification_email', '<EMAIL>', 'string', 'Email address for system notifications'),
            ('system_maintenance_mode', 'false', 'boolean', 'Enable maintenance mode to restrict access')
            ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value),
            updated_at = CURRENT_TIMESTAMP
        `);
        console.log('✅ Inserted default settings');

        // Create application_history table
        console.log('🔄 Creating application_history table...');
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS application_history (
                id INT PRIMARY KEY AUTO_INCREMENT,
                application_id INT NOT NULL,
                previous_status VARCHAR(50),
                new_status VARCHAR(50) NOT NULL,
                admin_id INT,
                admin_comments TEXT,
                action_type ENUM('status_change', 'comment_added', 'resubmission', 'document_update') NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
                FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL,
                INDEX idx_application_id (application_id),
                INDEX idx_admin_id (admin_id),
                INDEX idx_created_at (created_at)
            )
        `);
        console.log('✅ Created application_history table');

        // Update existing applications
        console.log('🔄 Updating existing applications...');
        await connection.execute(`
            UPDATE applications 
            SET editable = CASE 
                WHEN status = 'rejected' THEN TRUE 
                ELSE FALSE 
            END
        `);
        console.log('✅ Updated existing applications');

        // Add indexes
        console.log('🔄 Adding performance indexes...');
        try {
            await connection.execute(`CREATE INDEX idx_applications_status_editable ON applications(status, editable)`);
            await connection.execute(`CREATE INDEX idx_applications_user_status ON applications(user_id, status)`);
            console.log('✅ Added performance indexes');
        } catch (error) {
            if (error.code === 'ER_DUP_KEYNAME') {
                console.log('ℹ️ Indexes already exist');
            } else {
                throw error;
            }
        }

        // Verify the changes
        console.log('🔄 Verifying database structure...');
        const [applications] = await connection.execute('DESCRIBE applications');
        const [settings] = await connection.execute('DESCRIBE system_settings');
        const [history] = await connection.execute('DESCRIBE application_history');
        
        console.log('✅ Applications table structure verified');
        console.log('✅ System settings table structure verified');
        console.log('✅ Application history table structure verified');

        // Show current settings
        const [currentSettings] = await connection.execute('SELECT * FROM system_settings');
        console.log('📋 Current system settings:');
        currentSettings.forEach(setting => {
            console.log(`   ${setting.setting_key}: ${setting.setting_value} (${setting.setting_type})`);
        });

        console.log('🎉 Database update completed successfully!');

    } catch (error) {
        console.error('❌ Database update failed:', error.message);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔌 Database connection closed');
        }
    }
}

// Run the update
updateDatabase();
