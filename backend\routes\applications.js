const express = require('express');
const { executeQuery, executeTransaction } = require('../config/database');
const { verifyToken, requireCandidate, logActivity } = require('../middleware/auth');
const { validateApplication, validateId, validatePagination } = require('../middleware/validation');
const { generateApplicationId } = require('../utils/helpers');
const { sendEmail } = require('../utils/email');

const router = express.Router();

// @route   GET /api/applications
// @desc    Get user's applications
// @access  Private (Candidate)
router.get('/', verifyToken, requireCandidate, validatePagination, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        
        // Get applications with pagination
        const applications = await executeQuery(
            `SELECT id, application_id, name, current_course, institution_name,
                    scholarship_amount_figures, status, submitted_at, created_at, updated_at,
                    admin_notes, reviewed_by, reviewed_at
             FROM applications
             WHERE user_id = ?
             ORDER BY created_at DESC
             LIMIT ? OFFSET ?`,
            [req.user.id, limit, offset]
        );
        
        // Get total count
        const countResult = await executeQuery(
            'SELECT COUNT(*) as total FROM applications WHERE user_id = ?',
            [req.user.id]
        );
        
        const total = countResult[0].total;
        const totalPages = Math.ceil(total / limit);
        
        res.json({
            success: true,
            data: {
                applications,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages,
                    hasNext: page < totalPages,
                    hasPrev: page > 1
                }
            }
        });
        
    } catch (error) {
        console.error('Get applications error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch applications'
        });
    }
});

// @route   GET /api/applications/:id
// @desc    Get specific application
// @access  Private (Candidate)
router.get('/:id', verifyToken, requireCandidate, validateId, async (req, res) => {
    try {
        // Get application
        const applications = await executeQuery(
            'SELECT * FROM applications WHERE id = ? AND user_id = ?',
            [req.params.id, req.user.id]
        );
        
        if (applications.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Application not found'
            });
        }
        
        const application = applications[0];
        
        // Get family members
        const familyMembers = await executeQuery(
            'SELECT * FROM family_members WHERE application_id = ?',
            [application.id]
        );
        
        // Get disability details
        const disabilityDetails = await executeQuery(
            'SELECT * FROM disability_details WHERE application_id = ?',
            [application.id]
        );
        
        // Get educational qualifications
        const educationalQualifications = await executeQuery(
            'SELECT * FROM educational_qualifications WHERE application_id = ?',
            [application.id]
        );
        
        // Get file uploads
        const fileUploads = await executeQuery(
            'SELECT * FROM file_uploads WHERE application_id = ?',
            [application.id]
        );
        
        res.json({
            success: true,
            data: {
                application: {
                    ...application,
                    family_members: familyMembers,
                    disability_details: disabilityDetails[0] || null,
                    educational_qualifications: educationalQualifications,
                    file_uploads: fileUploads
                }
            }
        });
        
    } catch (error) {
        console.error('Get application error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch application'
        });
    }
});

// @route   POST /api/applications
// @desc    Create new application
// @access  Private (Candidate)
router.post('/', verifyToken, requireCandidate, validateApplication, logActivity('create_application'), async (req, res) => {
    try {
        const applicationData = req.body;
        const userId = req.user.id;
        
        // Check if user already has a pending/submitted application
        const existingApplications = await executeQuery(
            'SELECT id FROM applications WHERE user_id = ? AND status IN ("draft", "submitted", "under_review")',
            [userId]
        );
        
        if (existingApplications.length > 0) {
            return res.status(400).json({
                success: false,
                error: 'You already have an active application. Please complete or withdraw it before creating a new one.'
            });
        }
        
        // Generate unique application ID
        const applicationId = generateApplicationId();
        
        // Prepare transaction queries
        const queries = [];
        
        // Insert main application with all fields
        queries.push({
            query: `INSERT INTO applications (
                user_id, application_id, name, gender, date_of_birth, age, place_of_birth,
                marital_status, religion, category, nationality, present_address, present_state,
                present_country, permanent_address, permanent_state, permanent_country,
                phone, whatsapp, email, family_total_income,
                father_name, father_age, father_occupation, father_income, father_employment,
                mother_name, mother_age, mother_occupation, mother_income, mother_employment,
                spouse_name, spouse_age, spouse_occupation, spouse_income, spouse_employment,
                sibling1_name, sibling1_age, sibling1_occupation, sibling1_income, sibling1_employment,
                sibling2_name, sibling2_age, sibling2_occupation, sibling2_income, sibling2_employment,
                sibling3_name, sibling3_age, sibling3_occupation, sibling3_income, sibling3_employment,
                sibling4_name, sibling4_age, sibling4_occupation, sibling4_income, sibling4_employment,
                is_disabled, disability_type, disability_percentage, disability_description,
                issuing_authority, certificate_number, issue_date,
                sslc_institution, sslc_type, sslc_board, sslc_marks, sslc_year,
                hsc_institution, hsc_type, hsc_board, hsc_marks, hsc_year,
                ug_institution, ug_type, ug_board, ug_marks, ug_year,
                vocational_institution, vocational_type, vocational_board, vocational_marks, vocational_year,
                diploma_institution, diploma_type, diploma_board, diploma_marks, diploma_year,
                others_institution, others_type, others_board, others_marks, others_year,
                current_course, course_duration, course_year, roll_number,
                institution_name, institution_type, institution_address, institution_phone,
                institution_email, institution_website, term_fees, tuition_fees, other_fees,
                scholarship_amount_figures, scholarship_amount_words,
                previous_awt_scholarship, other_scholarships, applied_scholarships,
                reference1_name, reference1_phone, reference1_email, reference1_position,
                reference2_name, reference2_phone, reference2_email, reference2_position,
                extracurricular, other_info, goals,
                declaration_place, declaration_date, status
            ) VALUES (${Array(125).fill('?').join(', ')})`,
            params: [
                userId, applicationId, applicationData.name || '', applicationData.gender || '',
                applicationData.date_of_birth || null, applicationData.age || null, applicationData.place_of_birth || '',
                applicationData.marital_status || '', applicationData.religion || '', applicationData.category || '',
                applicationData.nationality || '', applicationData.present_address || '', applicationData.present_state || '',
                applicationData.present_country || '', applicationData.permanent_address || '', applicationData.permanent_state || '',
                applicationData.permanent_country || '', applicationData.phone || '', applicationData.whatsapp || '',
                applicationData.email || '', applicationData.family_total_income || 0,
                applicationData.father_name || '', applicationData.father_age || null, applicationData.father_occupation || '',
                applicationData.father_income || 0, applicationData.father_employment || '',
                applicationData.mother_name || '', applicationData.mother_age || null, applicationData.mother_occupation || '',
                applicationData.mother_income || 0, applicationData.mother_employment || '',
                applicationData.spouse_name || '', applicationData.spouse_age || null, applicationData.spouse_occupation || '',
                applicationData.spouse_income || 0, applicationData.spouse_employment || '',
                applicationData.sibling1_name || '', applicationData.sibling1_age || null, applicationData.sibling1_occupation || '',
                applicationData.sibling1_income || 0, applicationData.sibling1_employment || '',
                applicationData.sibling2_name || '', applicationData.sibling2_age || null, applicationData.sibling2_occupation || '',
                applicationData.sibling2_income || 0, applicationData.sibling2_employment || '',
                applicationData.sibling3_name || '', applicationData.sibling3_age || null, applicationData.sibling3_occupation || '',
                applicationData.sibling3_income || 0, applicationData.sibling3_employment || '',
                applicationData.sibling4_name || '', applicationData.sibling4_age || null, applicationData.sibling4_occupation || '',
                applicationData.sibling4_income || 0, applicationData.sibling4_employment || '',
                applicationData.is_disabled === true || applicationData.is_disabled === 'true' ? 1 : 0, applicationData.disability_type || '',
                applicationData.disability_percentage || null, applicationData.disability_description || '',
                applicationData.issuing_authority || '', applicationData.certificate_number || '', applicationData.issue_date || '',
                applicationData.sslc_institution || '', applicationData.sslc_type || '', applicationData.sslc_board || '',
                applicationData.sslc_marks || '', applicationData.sslc_year || null,
                applicationData.hsc_institution || '', applicationData.hsc_type || '', applicationData.hsc_board || '',
                applicationData.hsc_marks || '', applicationData.hsc_year || null,
                applicationData.ug_institution || '', applicationData.ug_type || '', applicationData.ug_board || '',
                applicationData.ug_marks || '', applicationData.ug_year || null,
                applicationData.vocational_institution || '', applicationData.vocational_type || '', applicationData.vocational_board || '',
                applicationData.vocational_marks || '', applicationData.vocational_year || null,
                applicationData.diploma_institution || '', applicationData.diploma_type || '', applicationData.diploma_board || '',
                applicationData.diploma_marks || '', applicationData.diploma_year || null,
                applicationData.others_institution || '', applicationData.others_type || '', applicationData.others_board || '',
                applicationData.others_marks || '', applicationData.others_year || null,
                applicationData.current_course || '', applicationData.course_duration || '', applicationData.course_year || '',
                applicationData.roll_number || '', applicationData.institution_name || '', applicationData.institution_type || '',
                applicationData.institution_address || '', applicationData.institution_phone || '', applicationData.institution_email || '',
                applicationData.institution_website || '', applicationData.term_fees || '', applicationData.tuition_fees || 0,
                applicationData.other_fees || 0, applicationData.scholarship_amount_figures || 0, applicationData.scholarship_amount_words || '',
                applicationData.previous_awt_scholarship || '', applicationData.other_scholarships || '',
                applicationData.applied_scholarships || '', applicationData.reference1_name || '', applicationData.reference1_phone || '',
                applicationData.reference1_email || '', applicationData.reference1_position || '', applicationData.reference2_name || '',
                applicationData.reference2_phone || '', applicationData.reference2_email || '', applicationData.reference2_position || '',
                applicationData.extracurricular || '', applicationData.other_info || '', applicationData.goals || '',
                applicationData.declaration_place || '', applicationData.declaration_date || null, 'draft'
            ]
        });
        
        // Execute transaction
        const results = await executeTransaction(queries);
        const newApplicationId = results[0].insertId;
        
        res.status(201).json({
            success: true,
            message: 'Application created successfully',
            data: {
                id: newApplicationId,
                application_id: applicationId,
                status: 'draft'
            }
        });
        
    } catch (error) {
        console.error('Create application error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to create application'
        });
    }
});

// @route   PUT /api/applications/:id
// @desc    Update application
// @access  Private (Candidate)
router.put('/:id', verifyToken, requireCandidate, validateId, validateApplication, logActivity('update_application'), async (req, res) => {
    try {
        const applicationData = req.body;
        const applicationId = req.params.id;
        
        // Check if application exists and belongs to user
        const applications = await executeQuery(
            'SELECT id, status FROM applications WHERE id = ? AND user_id = ?',
            [applicationId, req.user.id]
        );
        
        if (applications.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Application not found'
            });
        }
        
        const application = applications[0];
        
        // Check if application can be updated
        if (application.status === 'submitted' || application.status === 'under_review') {
            return res.status(400).json({
                success: false,
                error: 'Cannot update application that has been submitted or is under review'
            });
        }
        
        // Update application
        await executeQuery(
            `UPDATE applications SET 
                name = ?, gender = ?, date_of_birth = ?, age = ?, place_of_birth = ?,
                marital_status = ?, religion = ?, category = ?, nationality = ?,
                present_address = ?, present_state = ?, present_country = ?,
                permanent_address = ?, permanent_state = ?, permanent_country = ?,
                phone = ?, whatsapp = ?, email = ?, family_total_income = ?, is_disabled = ?,
                current_course = ?, course_duration = ?, course_year = ?, roll_number = ?,
                institution_name = ?, institution_type = ?, institution_address = ?,
                institution_phone = ?, institution_email = ?, scholarship_amount_figures = ?,
                scholarship_amount_words = ?, declaration_place = ?, declaration_date = ?,
                updated_at = NOW()
             WHERE id = ?`,
            [
                applicationData.name, applicationData.gender, applicationData.date_of_birth,
                applicationData.age, applicationData.place_of_birth, applicationData.marital_status,
                applicationData.religion, applicationData.category, applicationData.nationality,
                applicationData.present_address, applicationData.present_state, applicationData.present_country,
                applicationData.permanent_address, applicationData.permanent_state, applicationData.permanent_country,
                applicationData.phone, applicationData.whatsapp, applicationData.email,
                applicationData.family_total_income, applicationData.is_disabled, applicationData.current_course,
                applicationData.course_duration, applicationData.course_year, applicationData.roll_number,
                applicationData.institution_name, applicationData.institution_type, applicationData.institution_address,
                applicationData.institution_phone, applicationData.institution_email, applicationData.scholarship_amount_figures,
                applicationData.scholarship_amount_words, applicationData.declaration_place, applicationData.declaration_date,
                applicationId
            ]
        );
        
        res.json({
            success: true,
            message: 'Application updated successfully'
        });
        
    } catch (error) {
        console.error('Update application error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update application'
        });
    }
});

// @route   POST /api/applications/:id/submit
// @desc    Submit application for review
// @access  Private (Candidate)
router.post('/:id/submit', verifyToken, requireCandidate, validateId, logActivity('submit_application'), async (req, res) => {
    try {
        const applicationId = req.params.id;
        
        // Check if application exists and belongs to user
        const applications = await executeQuery(
            'SELECT id, status, name, email FROM applications WHERE id = ? AND user_id = ?',
            [applicationId, req.user.id]
        );
        
        if (applications.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Application not found'
            });
        }
        
        const application = applications[0];
        
        // Check if application can be submitted
        if (application.status !== 'draft') {
            return res.status(400).json({
                success: false,
                error: 'Application has already been submitted'
            });
        }
        
        // Update application status
        await executeQuery(
            'UPDATE applications SET status = "submitted", submitted_at = NOW() WHERE id = ?',
            [applicationId]
        );
        
        // Send confirmation email
        try {
            await sendEmail({
                to: application.email,
                subject: 'Application Submitted Successfully - Access Welfare Trust',
                template: 'application-submitted',
                data: {
                    name: application.name,
                    applicationId: application.application_id
                }
            });
        } catch (emailError) {
            console.error('Failed to send confirmation email:', emailError);
            // Don't fail the submission if email fails
        }
        
        res.json({
            success: true,
            message: 'Application submitted successfully'
        });
        
    } catch (error) {
        console.error('Submit application error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to submit application'
        });
    }
});

// @route   DELETE /api/applications/:id
// @desc    Delete application (only if draft)
// @access  Private (Candidate)
router.delete('/:id', verifyToken, requireCandidate, validateId, logActivity('delete_application'), async (req, res) => {
    try {
        const applicationId = req.params.id;
        
        // Check if application exists and belongs to user
        const applications = await executeQuery(
            'SELECT id, status FROM applications WHERE id = ? AND user_id = ?',
            [applicationId, req.user.id]
        );
        
        if (applications.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Application not found'
            });
        }
        
        const application = applications[0];
        
        // Check if application can be deleted
        if (application.status !== 'draft') {
            return res.status(400).json({
                success: false,
                error: 'Cannot delete application that has been submitted'
            });
        }
        
        // Delete application (cascade will handle related records)
        await executeQuery('DELETE FROM applications WHERE id = ?', [applicationId]);
        
        res.json({
            success: true,
            message: 'Application deleted successfully'
        });
        
    } catch (error) {
        console.error('Delete application error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to delete application'
        });
    }
});

// Update application status (admin only)
router.put('/:id/status', verifyToken, async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;

        console.log(`📝 Updating application ${id} status to: ${status}`);

        // Validate status
        const validStatuses = ['draft', 'submitted', 'under_review', 'approved', 'rejected'];
        if (!validStatuses.includes(status)) {
            return res.status(400).json({
                success: false,
                error: 'Invalid status'
            });
        }

        // Update application status
        const result = await executeQuery(
            'UPDATE applications SET status = ?, updated_at = NOW() WHERE id = ?',
            [status, id]
        );

        if (result.affectedRows === 0) {
            return res.status(404).json({
                success: false,
                error: 'Application not found'
            });
        }

        console.log(`✅ Application ${id} status updated to: ${status}`);

        res.json({
            success: true,
            message: `Application status updated to ${status}`,
            data: { id, status }
        });

    } catch (error) {
        console.error('❌ Error updating application status:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to update application status'
        });
    }
});

// Get single application by ID
router.get('/:id', verifyToken, async (req, res) => {
    try {
        const { id } = req.params;

        console.log(`🔍 Fetching application details for ID: ${id}`);

        const rows = await executeQuery(
            'SELECT * FROM applications WHERE id = ?',
            [id]
        );

        if (rows.length === 0) {
            return res.status(404).json({
                success: false,
                error: 'Application not found'
            });
        }

        console.log(`✅ Application details fetched for ID: ${id}`);

        res.json({
            success: true,
            data: rows[0]
        });

    } catch (error) {
        console.error('❌ Error fetching application details:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to fetch application details'
        });
    }
});

module.exports = router;
