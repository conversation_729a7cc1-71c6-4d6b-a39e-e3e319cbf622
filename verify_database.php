<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Verification - Trust Scholarship</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #28a745; color: white; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .check-item { display: flex; align-items: center; margin: 5px 0; }
        .check-icon { margin-right: 10px; font-size: 18px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Database Verification - Trust Scholarship System</h1>
            <p>Comprehensive verification of database structure and alignment</p>
        </div>

        <?php
        try {
            $pdo = new PDO("mysql:host=localhost;dbname=trust_scholarship_db", "root", "");
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            echo '<div class="status success">✅ Database connection successful!</div>';
            
            // 1. Verify file_uploads table structure
            echo '<div class="section">';
            echo '<h3>📁 File Uploads Table Verification</h3>';
            
            $stmt = $pdo->query("SHOW COLUMNS FROM file_uploads WHERE Field = 'file_type'");
            $column = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $requiredFileTypes = [
                'photo' => 'Passport size photograph (Step 1)',
                'marksheets' => 'All marksheets (Step 3)', 
                'fee_circular' => 'Institute official fee circular (Step 4)',
                'signature' => 'Digital signature (Step 5)',
                'photo_id' => 'Photo ID (Aadhaar/Passport/Driving License)',
                'institute_id' => 'Institute ID card',
                'address_proof' => 'Address proof documents',
                'aadhaar_card' => 'Aadhaar card copy',
                'birth_certificate' => 'Birth certificate',
                'community_certificate' => 'Community/Caste certificate',
                'course_details' => 'Course fee details',
                'income_certificate' => 'Family income certificate',
                'attendance_certificate' => 'Attendance certificate (optional)',
                'fee_receipts' => 'Fee receipts 2025-26 (optional)',
                'disability_certificate' => 'Disability certificate (conditional)',
                'other' => 'Other documents'
            ];
            
            $currentEnum = $column['Type'];
            $allPresent = true;
            
            echo '<table>';
            echo '<tr><th>File Type</th><th>Description</th><th>Status</th></tr>';
            
            foreach ($requiredFileTypes as $type => $description) {
                $isPresent = strpos($currentEnum, "'$type'") !== false;
                $status = $isPresent ? '<span style="color: green;">✅ Present</span>' : '<span style="color: red;">❌ Missing</span>';
                if (!$isPresent) $allPresent = false;
                
                echo '<tr>';
                echo '<td><code>' . htmlspecialchars($type) . '</code></td>';
                echo '<td>' . htmlspecialchars($description) . '</td>';
                echo '<td>' . $status . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            
            if ($allPresent) {
                echo '<div class="status success">✅ All 16 required file types are present in the database!</div>';
            } else {
                echo '<div class="status error">❌ Some file types are missing. Please run the database updater.</div>';
            }
            echo '</div>';
            
            // 2. Verify applications table structure
            echo '<div class="section">';
            echo '<h3>📋 Applications Table Verification</h3>';
            
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'trust_scholarship_db' AND TABLE_NAME = 'applications'");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $columnCount = $result['count'];
            
            echo '<div class="check-item">';
            echo '<span class="check-icon">📊</span>';
            echo '<span>Applications table has <strong>' . $columnCount . '</strong> columns</span>';
            echo '</div>';
            
            // Check key fields
            $requiredFields = [
                'name' => 'Applicant name',
                'gender' => 'Gender',
                'date_of_birth' => 'Date of birth',
                'phone' => 'Phone number',
                'email' => 'Email address',
                'status' => 'Application status',
                'father_name' => 'Father name',
                'mother_name' => 'Mother name',
                'current_course' => 'Current course',
                'institution_name' => 'Institution name',
                'scholarship_amount_figures' => 'Scholarship amount'
            ];
            
            $stmt = $pdo->query("DESCRIBE applications");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $existingFields = array_column($columns, 'Field');
            
            echo '<h4>Key Fields Verification:</h4>';
            echo '<table>';
            echo '<tr><th>Field</th><th>Description</th><th>Status</th></tr>';
            
            $allFieldsPresent = true;
            foreach ($requiredFields as $field => $description) {
                $isPresent = in_array($field, $existingFields);
                $status = $isPresent ? '<span style="color: green;">✅ Present</span>' : '<span style="color: red;">❌ Missing</span>';
                if (!$isPresent) $allFieldsPresent = false;
                
                echo '<tr>';
                echo '<td><code>' . htmlspecialchars($field) . '</code></td>';
                echo '<td>' . htmlspecialchars($description) . '</td>';
                echo '<td>' . $status . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            
            if ($allFieldsPresent) {
                echo '<div class="status success">✅ All key application fields are present!</div>';
            } else {
                echo '<div class="status error">❌ Some key fields are missing.</div>';
            }
            echo '</div>';
            
            // 3. Check form-to-database alignment
            echo '<div class="section">';
            echo '<h3>🔗 Form-to-Database Alignment</h3>';
            
            $formSteps = [
                'Step 1: Personal Details' => ['name', 'gender', 'date_of_birth', 'age', 'phone', 'email'],
                'Step 2: Family Details' => ['father_name', 'mother_name', 'family_total_income'],
                'Step 3: Educational Details' => ['sslc_institution', 'hsc_institution', 'current_course'],
                'Step 4: Scholarship Details' => ['institution_name', 'scholarship_amount_figures'],
                'Step 5: Documents & Declaration' => ['declaration_place', 'declaration_date']
            ];
            
            foreach ($formSteps as $step => $fields) {
                echo '<h4>' . $step . '</h4>';
                $stepComplete = true;
                
                foreach ($fields as $field) {
                    $isPresent = in_array($field, $existingFields);
                    $icon = $isPresent ? '✅' : '❌';
                    if (!$isPresent) $stepComplete = false;
                    
                    echo '<div class="check-item">';
                    echo '<span class="check-icon">' . $icon . '</span>';
                    echo '<span><code>' . $field . '</code></span>';
                    echo '</div>';
                }
                
                if ($stepComplete) {
                    echo '<div class="status success">✅ ' . $step . ' - All fields mapped correctly</div>';
                } else {
                    echo '<div class="status warning">⚠️ ' . $step . ' - Some fields missing</div>';
                }
            }
            echo '</div>';
            
            // 4. Database statistics
            echo '<div class="section">';
            echo '<h3>📊 Database Statistics</h3>';
            
            // Count applications
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM applications");
            $appCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            // Count users
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
            $userCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            // Count file uploads
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM file_uploads");
            $fileCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            echo '<table>';
            echo '<tr><th>Table</th><th>Record Count</th></tr>';
            echo '<tr><td>Applications</td><td>' . $appCount . '</td></tr>';
            echo '<tr><td>Users</td><td>' . $userCount . '</td></tr>';
            echo '<tr><td>File Uploads</td><td>' . $fileCount . '</td></tr>';
            echo '</table>';
            echo '</div>';
            
            // 5. Overall status
            echo '<div class="section">';
            echo '<h3>🎯 Overall System Status</h3>';
            
            if ($allPresent && $allFieldsPresent) {
                echo '<div class="status success">';
                echo '<h4>🎉 Database is fully aligned and ready!</h4>';
                echo '<ul>';
                echo '<li>✅ All 16 file types are supported</li>';
                echo '<li>✅ All application form fields are mapped</li>';
                echo '<li>✅ Database structure is complete</li>';
                echo '<li>✅ System is ready for production use</li>';
                echo '</ul>';
                echo '</div>';
            } else {
                echo '<div class="status warning">';
                echo '<h4>⚠️ Database needs attention</h4>';
                echo '<p>Some components are missing. Please run the database updater to fix issues.</p>';
                echo '</div>';
            }
            echo '</div>';
            
        } catch (PDOException $e) {
            echo '<div class="status error">❌ Database connection failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
            echo '<div class="info">Please ensure XAMPP MySQL is running and the database exists.</div>';
        }
        ?>

        <div style="margin-top: 20px; text-align: center;">
            <a href="database_updater.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">🔄 Database Updater</a>
            <a href="application.html" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">📝 Application Form</a>
            <a href="admin/dashboard.html" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">👨‍💼 Admin Dashboard</a>
        </div>
    </div>
</body>
</html>
