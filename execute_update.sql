-- Execute this SQL script in phpMyAdmin or MySQL command line
-- This will update the file_uploads table to include all required file types

USE trust_scholarship_db;

-- Check current structure
SELECT 'Current file_uploads table structure:' as info;
DESCRIBE file_uploads;

-- Show current ENUM values
SELECT 'Current file_type ENUM values:' as info;
SHOW COLUMNS FROM file_uploads WHERE Field = 'file_type';

-- Update the file_type ENUM to include all document types
ALTER TABLE file_uploads 
MODIFY COLUMN file_type ENUM(
    'photo',                    -- Passport size photograph (Step 1)
    'marksheets',              -- All marksheets (Step 3)
    'fee_circular',            -- Institute official fee circular (Step 4)
    'signature',               -- Digital signature (Step 5)
    'photo_id',                -- Photo ID (Aadhaar/Passport/Driving License)
    'institute_id',            -- Institute ID card
    'address_proof',           -- Address proof (Utility bill/Bank statement)
    'aadhaar_card',           -- A<PERSON><PERSON>ar card copy
    'birth_certificate',       -- Birth certificate
    'community_certificate',   -- Community/Caste certificate
    'course_details',          -- Course fee details
    'income_certificate',      -- Family income certificate
    'attendance_certificate',  -- Attendance certificate (optional)
    'fee_receipts',            -- Fee receipts 2025-26 (optional)
    'disability_certificate',  -- Disability certificate (conditional)
    'other'                    -- Other documents
) NOT NULL;

-- Verify the update
SELECT 'Updated file_uploads table structure:' as info;
DESCRIBE file_uploads;

-- Show updated ENUM values
SELECT 'Updated file_type ENUM values:' as info;
SHOW COLUMNS FROM file_uploads WHERE Field = 'file_type';

-- Check current file types in use
SELECT 'Current file types in database:' as info;
SELECT file_type, COUNT(*) as count 
FROM file_uploads 
GROUP BY file_type 
ORDER BY file_type;

-- Verify applications table structure
SELECT 'Applications table column count:' as info;
SELECT COUNT(*) as column_count 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'trust_scholarship_db' 
AND TABLE_NAME = 'applications';

SELECT 'Database update completed successfully!' as result;
