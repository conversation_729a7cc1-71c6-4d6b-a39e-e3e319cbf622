const mysql = require('mysql2/promise');
require('dotenv').config();

// Database connection configuration
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    charset: 'utf8mb4'
};

// SQL Migration Scripts
const migrations = [
    // Create database
    `CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME || 'trust_scholarship_db'} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`,
    
    // Use database
    `USE ${process.env.DB_NAME || 'trust_scholarship_db'}`,
    
    // Users table
    `CREATE TABLE IF NOT EXISTS users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        phone VARCHAR(20),
        password VARCHAR(255) NOT NULL,
        role ENUM('candidate', 'admin') DEFAULT 'candidate',
        email_verified BOOLEAN DEFAULT FALSE,
        verification_token VARCHAR(255),
        reset_token VARCHAR(255),
        reset_token_expires DATETIME,
        last_login DATETIME,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_email (email),
        INDEX idx_role (role),
        INDEX idx_created_at (created_at)
    )`,
    
    // Applications table
    `CREATE TABLE IF NOT EXISTS applications (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        application_id VARCHAR(50) UNIQUE NOT NULL,
        
        -- Personal Details
        name VARCHAR(255) NOT NULL,
        gender ENUM('Male', 'Female', 'Other') NOT NULL,
        date_of_birth DATE NOT NULL,
        age INT NOT NULL,
        place_of_birth VARCHAR(255) NOT NULL,
        marital_status ENUM('Single', 'Married', 'Divorced', 'Widowed') NOT NULL,
        religion VARCHAR(100) NOT NULL,
        category ENUM('BC', 'MBC', 'SC', 'ST', 'General') NOT NULL,
        nationality VARCHAR(100) NOT NULL,
        
        -- Address Details
        present_address TEXT NOT NULL,
        present_state VARCHAR(100) NOT NULL,
        present_country VARCHAR(100) NOT NULL,
        permanent_address TEXT NOT NULL,
        permanent_state VARCHAR(100) NOT NULL,
        permanent_country VARCHAR(100) NOT NULL,
        
        -- Contact Details
        phone VARCHAR(20) NOT NULL,
        whatsapp VARCHAR(20),
        email VARCHAR(255) NOT NULL,
        
        -- Family Details
        family_total_income DECIMAL(12,2) NOT NULL,
        is_disabled BOOLEAN DEFAULT FALSE,
        
        -- Educational Details
        current_course VARCHAR(255) NOT NULL,
        course_duration VARCHAR(100) NOT NULL,
        course_year VARCHAR(50) NOT NULL,
        roll_number VARCHAR(100) NOT NULL,
        institution_name VARCHAR(255) NOT NULL,
        institution_type ENUM('Government', 'Aided', 'Private') NOT NULL,
        institution_address TEXT NOT NULL,
        institution_phone VARCHAR(20) NOT NULL,
        institution_email VARCHAR(255) NOT NULL,
        
        -- Scholarship Details
        scholarship_amount_figures DECIMAL(10,2) NOT NULL,
        scholarship_amount_words VARCHAR(500) NOT NULL,
        
        -- Declaration
        declaration_place VARCHAR(255) NOT NULL,
        declaration_date DATE NOT NULL,
        
        -- Application Status
        status ENUM('draft', 'submitted', 'under_review', 'approved', 'rejected') DEFAULT 'draft',
        admin_notes TEXT,
        reviewed_by INT,
        reviewed_at DATETIME,
        
        -- Timestamps
        submitted_at DATETIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (reviewed_by) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_user_id (user_id),
        INDEX idx_application_id (application_id),
        INDEX idx_status (status),
        INDEX idx_created_at (created_at)
    )`,
    
    // Family members table
    `CREATE TABLE IF NOT EXISTS family_members (
        id INT PRIMARY KEY AUTO_INCREMENT,
        application_id INT NOT NULL,
        relationship ENUM('Father', 'Mother', 'Spouse', 'Sibling') NOT NULL,
        name VARCHAR(255),
        age INT,
        occupation VARCHAR(255),
        annual_income DECIMAL(10,2),
        employment_details VARCHAR(500),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
        INDEX idx_application_id (application_id)
    )`,
    
    // Disability details table
    `CREATE TABLE IF NOT EXISTS disability_details (
        id INT PRIMARY KEY AUTO_INCREMENT,
        application_id INT NOT NULL,
        disability_type VARCHAR(255) NOT NULL,
        disability_percentage INT NOT NULL,
        disability_description TEXT,
        issuing_authority VARCHAR(255),
        certificate_number VARCHAR(255),
        issue_date_place VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
        INDEX idx_application_id (application_id)
    )`,
    
    // Educational qualifications table
    `CREATE TABLE IF NOT EXISTS educational_qualifications (
        id INT PRIMARY KEY AUTO_INCREMENT,
        application_id INT NOT NULL,
        qualification_type ENUM('SSLC', 'HSC', 'UG', 'Vocational', 'Diploma', 'Others') NOT NULL,
        institution_name VARCHAR(255),
        institution_type ENUM('Government', 'Private'),
        board_university VARCHAR(255),
        marks_grade VARCHAR(100),
        year_of_passing INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
        INDEX idx_application_id (application_id)
    )`,
    
    // File uploads table
    `CREATE TABLE IF NOT EXISTS file_uploads (
        id INT PRIMARY KEY AUTO_INCREMENT,
        application_id INT NOT NULL,
        file_type ENUM('photo', 'signature', 'photo_id', 'institute_id', 'address_proof',
                      'income_certificate', 'disability_certificate', 'marksheets', 'fee_circular',
                      'aadhaar_card', 'birth_certificate', 'community_certificate', 'course_details',
                      'attendance_certificate', 'fee_receipts', 'other') NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        file_name VARCHAR(255) NOT NULL,
        file_path VARCHAR(500) NOT NULL,
        file_size INT NOT NULL,
        mime_type VARCHAR(100) NOT NULL,
        uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
        INDEX idx_application_id (application_id),
        INDEX idx_file_type (file_type)
    )`,
    
    // Notifications table
    `CREATE TABLE IF NOT EXISTS notifications (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_id (user_id),
        INDEX idx_is_read (is_read),
        INDEX idx_created_at (created_at)
    )`,
    
    // Audit log table
    `CREATE TABLE IF NOT EXISTS audit_logs (
        id INT PRIMARY KEY AUTO_INCREMENT,
        user_id INT,
        action VARCHAR(100) NOT NULL,
        table_name VARCHAR(100) NOT NULL,
        record_id INT,
        old_values JSON,
        new_values JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_user_id (user_id),
        INDEX idx_action (action),
        INDEX idx_table_name (table_name),
        INDEX idx_created_at (created_at)
    )`
];

// Run migrations
const runMigrations = async () => {
    let connection;

    try {
        console.log('🚀 Starting database migration...');

        // Connect to MySQL server (without database)
        connection = await mysql.createConnection(dbConfig);

        // Create database first (can't use prepared statement)
        console.log(`📝 Running migration 1/${migrations.length}...`);
        await connection.query(migrations[0]);

        // Close connection and reconnect with database
        await connection.end();
        connection = await mysql.createConnection({
            ...dbConfig,
            database: process.env.DB_NAME || 'trust_scholarship_db'
        });

        // Execute remaining migrations (skip first two - database creation and USE statement)
        for (let i = 2; i < migrations.length; i++) {
            console.log(`📝 Running migration ${i + 1}/${migrations.length}...`);
            await connection.execute(migrations[i]);
        }

        console.log('✅ All migrations completed successfully!');

    } catch (error) {
        console.error('❌ Migration failed:', error);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
        }
    }
};

// Create default admin user
const createDefaultAdmin = async () => {
    try {
        const bcrypt = require('bcryptjs');
        const connection = await mysql.createConnection({
            ...dbConfig,
            database: process.env.DB_NAME || 'trust_scholarship_db'
        });

        const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
        const adminPassword = process.env.ADMIN_PASSWORD || 'Admin@123';
        const hashedPassword = await bcrypt.hash(adminPassword, 12);

        // Check if admin already exists
        const [existing] = await connection.execute(
            'SELECT id FROM users WHERE email = ? AND role = "admin"',
            [adminEmail]
        );

        if (existing.length === 0) {
            await connection.execute(
                `INSERT INTO users (name, email, password, role, email_verified, is_active)
                 VALUES (?, ?, ?, 'admin', TRUE, TRUE)`,
                ['Admin User', adminEmail, hashedPassword]
            );
            console.log('✅ Default admin user created');
            console.log(`📧 Email: ${adminEmail}`);
            console.log(`🔑 Password: ${adminPassword}`);
        } else {
            console.log('ℹ️  Admin user already exists');
        }

        await connection.end();
    } catch (error) {
        console.error('❌ Failed to create admin user:', error);
    }
};

// Run if called directly
if (require.main === module) {
    runMigrations().then(() => {
        createDefaultAdmin();
    });
}

module.exports = { runMigrations, createDefaultAdmin };
