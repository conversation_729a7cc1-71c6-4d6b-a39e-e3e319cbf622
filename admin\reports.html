<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports - Admin Dashboard</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Dashboard Styles */
        .dashboard-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 180px);
        }

        /* Sidebar Styles */
        .sidebar {
            background-color: #2c3e50;
            color: white;
            padding: 2rem 0;
            position: relative;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            padding: 0;
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background-color: #34495e;
        }

        .sidebar-menu i {
            margin-right: 1rem;
            width: 20px;
            text-align: center;
        }

        .user-info {
            padding: 1rem 2rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid #34495e;
        }

        .user-name {
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .user-role {
            font-size: 0.9rem;
            color: #bdc3c7;
        }

        /* Logout Button */
        .logout-button {
            margin-top: auto;
            padding: 1rem 2rem;
            background-color: #c0392b;
            color: white;
            border: none;
            cursor: pointer;
            width: 100%;
            text-align: left;
            display: flex;
            align-items: center;
        }

        .logout-button i {
            margin-right: 1rem;
        }

        /* Main Content */
        .main-content {
            padding: 2rem;
            background-color: #f8f9fa;
            min-height: calc(100vh - 180px);
            overflow-y: auto;
        }

        /* Scrollbar Styling */
        .main-content::-webkit-scrollbar {
            width: 8px;
        }

        .main-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .main-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .main-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .page-title {
            margin-bottom: 2rem;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .page-title i {
            color: #17a2b8;
        }

        /* Report Cards */
        .reports-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .report-card {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }

        .report-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .report-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .report-icon {
            font-size: 2rem;
            padding: 1rem;
            border-radius: 50%;
        }

        .report-icon.applications { background-color: #e3f2fd; color: #1976d2; }
        .report-icon.financial { background-color: #e8f5e8; color: #388e3c; }
        .report-icon.users { background-color: #fff3e0; color: #f57c00; }
        .report-icon.analytics { background-color: #f3e5f5; color: #7b1fa2; }

        .report-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
        }

        .report-description {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.5;
        }

        .report-actions {
            display: flex;
            gap: 1rem;
        }

        .btn-generate, .btn-download {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-generate {
            background-color: #007bff;
            color: white;
            flex: 1;
        }

        .btn-download {
            background-color: #28a745;
            color: white;
        }

        .btn-generate:hover { background-color: #0056b3; }
        .btn-download:hover { background-color: #1e7e34; }

        /* Quick Stats */
        .quick-stats {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 2rem;
            text-align: center;
        }

        .stat-item h3 {
            font-size: 2rem;
            margin: 0;
            color: #2c3e50;
        }

        .stat-item p {
            margin: 0.5rem 0 0;
            color: #666;
        }

        /* Date Range Selector */
        .date-range-section {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .date-range-form {
            display: flex;
            gap: 1rem;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .btn-apply {
            background-color: #17a2b8;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            height: fit-content;
        }

        .btn-apply:hover {
            background-color: #138496;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-container">
            <div class="logo-container">
                <img src="../images/awt-logo.png" alt="Access Welfare Trust Logo" class="logo">
                <div>
                    <h1>Access Welfare Trust</h1>
                    <p class="tagline">Connect, Empower & Sustain</p>
                </div>
            </div>
            <div class="header-right">
                <a href="dashboard.html" class="admin-link active">Admin Dashboard</a>
            </div>
        </div>
    </header>

    <div class="dashboard-container">
        <aside class="sidebar">
            <div class="user-info">
                <div class="user-name">Admin User</div>
                <div class="user-role">Administrator</div>
            </div>

            <ul class="sidebar-menu">
                <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="applications.html"><i class="fas fa-file-alt"></i> Applications</a></li>
                <li><a href="approved.html"><i class="fas fa-check-circle"></i> Approved</a></li>
                <li><a href="rejected.html"><i class="fas fa-times-circle"></i> Rejected</a></li>
                <li><a href="applicants.html"><i class="fas fa-users"></i> Applicants</a></li>
                <li><a href="reports.html" class="active"><i class="fas fa-chart-bar"></i> Reports</a></li>
                <li><a href="settings.html"><i class="fas fa-cog"></i> Settings</a></li>
            </ul>

            <button id="logoutButton" class="logout-button">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </aside>

        <main class="main-content">
            <h1 class="page-title">
                <i class="fas fa-chart-bar"></i>
                Reports & Analytics
            </h1>

            <!-- Date Range Selector -->
            <div class="date-range-section">
                <h3>Report Date Range</h3>
                <div class="date-range-form">
                    <div class="form-group">
                        <label for="startDate">Start Date</label>
                        <input type="date" id="startDate">
                    </div>
                    <div class="form-group">
                        <label for="endDate">End Date</label>
                        <input type="date" id="endDate">
                    </div>
                    <button class="btn-apply" onclick="updateDateRange()">
                        <i class="fas fa-sync"></i> Apply
                    </button>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="quick-stats">
                <h3>Quick Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <h3 id="totalApplications">0</h3>
                        <p>Total Applications</p>
                    </div>
                    <div class="stat-item">
                        <h3 id="totalApproved">0</h3>
                        <p>Approved</p>
                    </div>
                    <div class="stat-item">
                        <h3 id="totalAmount">₹0</h3>
                        <p>Total Amount</p>
                    </div>
                    <div class="stat-item">
                        <h3 id="avgAmount">₹0</h3>
                        <p>Average Amount</p>
                    </div>
                </div>
            </div>

            <!-- Report Cards -->
            <div class="reports-grid">
                <!-- Applications Report -->
                <div class="report-card">
                    <div class="report-header">
                        <div class="report-icon applications">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <h3 class="report-title">Applications Report</h3>
                    </div>
                    <p class="report-description">
                        Comprehensive report of all scholarship applications including status breakdown, submission trends, and processing times.
                    </p>
                    <div class="report-actions">
                        <button class="btn-generate" onclick="generateReport('applications')">
                            <i class="fas fa-chart-line"></i> Generate
                        </button>
                        <button class="btn-download" onclick="downloadReport('applications')">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>

                <!-- Financial Report -->
                <div class="report-card">
                    <div class="report-header">
                        <div class="report-icon financial">
                            <i class="fas fa-rupee-sign"></i>
                        </div>
                        <h3 class="report-title">Financial Report</h3>
                    </div>
                    <p class="report-description">
                        Financial analysis including total disbursements, approved amounts, budget utilization, and funding distribution.
                    </p>
                    <div class="report-actions">
                        <button class="btn-generate" onclick="generateReport('financial')">
                            <i class="fas fa-chart-pie"></i> Generate
                        </button>
                        <button class="btn-download" onclick="downloadReport('financial')">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>

                <!-- User Analytics -->
                <div class="report-card">
                    <div class="report-header">
                        <div class="report-icon users">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="report-title">User Analytics</h3>
                    </div>
                    <p class="report-description">
                        User engagement metrics, registration trends, application patterns, and demographic analysis of applicants.
                    </p>
                    <div class="report-actions">
                        <button class="btn-generate" onclick="generateReport('users')">
                            <i class="fas fa-chart-area"></i> Generate
                        </button>
                        <button class="btn-download" onclick="downloadReport('users')">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>

                <!-- Performance Analytics -->
                <div class="report-card">
                    <div class="report-header">
                        <div class="report-icon analytics">
                            <i class="fas fa-analytics"></i>
                        </div>
                        <h3 class="report-title">Performance Analytics</h3>
                    </div>
                    <p class="report-description">
                        System performance metrics, processing efficiency, approval rates, and administrative workflow analysis.
                    </p>
                    <div class="report-actions">
                        <button class="btn-generate" onclick="generateReport('performance')">
                            <i class="fas fa-tachometer-alt"></i> Generate
                        </button>
                        <button class="btn-download" onclick="downloadReport('performance')">
                            <i class="fas fa-download"></i> Export
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <footer>
        <p>&copy; 2025 Access Welfare Trust. All rights reserved.</p>
    </footer>

    <script src="../js/api-config.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check admin authentication
            const authToken = localStorage.getItem('authToken');
            if (!authToken) {
                alert('Please login as admin to access this page');
                window.location.href = '../login-signup.html';
                return;
            }

            // Set default date range (last 30 days)
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);
            
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];

            loadQuickStats();
            setupEventListeners();
        });

        function setupEventListeners() {
            // Logout functionality
            const logoutButton = document.getElementById('logoutButton');
            if (logoutButton) {
                logoutButton.addEventListener('click', async function() {
                    if (confirm('Are you sure you want to logout?')) {
                        try {
                            const authToken = localStorage.getItem('authToken');
                            if (authToken) {
                                await fetch('http://localhost:3000/api/auth/logout', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': `Bearer ${authToken}`
                                    }
                                });
                            }
                        } catch (error) {
                            console.error('Logout API call failed:', error);
                        }

                        localStorage.removeItem('authToken');
                        localStorage.removeItem('adminLoggedIn');
                        window.location.href = '../login-signup.html';
                    }
                });
            }
        }

        async function loadQuickStats() {
            try {
                const authToken = localStorage.getItem('authToken');
                const response = await apiCall('/api/admin/dashboard', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    const stats = result.data.statistics;
                    
                    document.getElementById('totalApplications').textContent = stats.total_applications || 0;
                    document.getElementById('totalApproved').textContent = stats.approved_applications || 0;
                    
                    // Calculate financial stats (placeholder)
                    const avgAmount = 25000; // This would come from API
                    const totalAmount = (stats.approved_applications || 0) * avgAmount;
                    
                    document.getElementById('totalAmount').textContent = `₹${totalAmount.toLocaleString()}`;
                    document.getElementById('avgAmount').textContent = `₹${avgAmount.toLocaleString()}`;
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        function updateDateRange() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (!startDate || !endDate) {
                alert('Please select both start and end dates');
                return;
            }
            
            if (new Date(startDate) > new Date(endDate)) {
                alert('Start date cannot be after end date');
                return;
            }
            
            alert(`Date range updated: ${startDate} to ${endDate}\nReports will be generated for this period.`);
            loadQuickStats();
        }

        function generateReport(reportType) {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            alert(`Generating ${reportType} report for period: ${startDate} to ${endDate}\n\nThis feature will be implemented to show detailed analytics and charts.`);
            
            // TODO: Implement actual report generation
            // This would typically:
            // 1. Call API to get report data
            // 2. Generate charts/graphs
            // 3. Display in modal or new page
        }

        function downloadReport(reportType) {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            // Create sample CSV data
            const headers = ['Date', 'Metric', 'Value'];
            const sampleData = [
                ['2025-01-01', 'Applications Submitted', '15'],
                ['2025-01-02', 'Applications Approved', '8'],
                ['2025-01-03', 'Total Amount Disbursed', '200000']
            ];
            
            const csvContent = [
                headers.join(','),
                ...sampleData.map(row => row.join(','))
            ].join('\n');

            // Download CSV
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${reportType}_report_${startDate}_to_${endDate}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
